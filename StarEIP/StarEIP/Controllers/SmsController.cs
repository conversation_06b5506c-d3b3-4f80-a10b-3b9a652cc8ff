using Flurl.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Models;
using StarEIP.Configuration;
using StarEIP.Models;
using StarEIP.Models.App.PhoneApi;
using StarEIP.Models.App.TelebroadApi;
using System.Text.Json;

namespace StarEIP.Controllers;

[Route("api/sms")]
[ApiController]
[Authorize]
public class SmsController : ControllerBase
{
    private readonly ILogger<SmsController> _logger;
    private readonly StarEipDbContext _dbContext;
    private readonly TelebroadApiOptions _telebroadOptions;
    private const string SystemPhone = "+17187871023";

    public SmsController(
        ILogger<SmsController> logger,
        StarEipDbContext dbContext,
        IOptions<TelebroadApiOptions> telebroadOptions)
    {
        _logger = logger;
        _dbContext = dbContext;
        _telebroadOptions = telebroadOptions.Value;
    }

    [HttpGet("messages/{childId}")]
    public async Task<IActionResult> GetMessagesAsync(int childId)
    {
        try
        {
            var child = await _dbContext.Children
                .Where(c => c.Id == childId)
                .Select(c => new { c.ParentPhoneNumber })
                .FirstOrDefaultAsync();

            if (child == null) return NotFound();

            if(string.IsNullOrEmpty(child.ParentPhoneNumber)) return Ok(Array.Empty<object>());

            var parentPhoneNumber = FormatPhoneNumber(child.ParentPhoneNumber);

            var messageQuery = _dbContext.Messages
                .Where(m => (m.From == parentPhoneNumber && m.To == SystemPhone)
                            || (m.From == SystemPhone && m.To == parentPhoneNumber));

            var messages = await messageQuery
                .OrderBy(m => m.CreatedAt)
                .ThenBy(m => m.Id)
                .Take(100)
                .Select(m => new
                {
                    m.Id,
                    m.From,
                    m.To,
                    m.Text,
                    m.CreatedAt,
                    m.Direction,
                })
                .ToListAsync();

            return Ok(messages);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to get messages for child {childId}.", childId);
            throw;
        }
    }


    public record SendSmsRequest(string To, string Message, int ChildId);

    [HttpPost("send")]
    public async Task<IActionResult> SendSmsAsync([FromBody] SendSmsRequest request)
    {
        try
        {
            // Telebroad API expects a different format for sending SMS
            var response = await $"{_telebroadOptions.BaseUrl}/send/sms"
                .WithHeader("token", _telebroadOptions.Password)
                .WithHeader("mailbox", _telebroadOptions.FaxNumber)
                .WithBasicAuth(_telebroadOptions.Username, _telebroadOptions.Password)
                .PostJsonAsync(new
                {
                    from = SystemPhone.TrimStart('+'),
                    to = request.To.TrimStart('+'),
                    text = request.Message
                });

            var responseContent = await response.GetStringAsync();
            var telebroadResponse = JsonSerializer.Deserialize<TelebroadSmsResponse>(responseContent);

            if (telebroadResponse.error != null)
            {
                throw new Exception($"Failed to send SMS: {telebroadResponse.error}");
            }

            var newMessage = new SmsMessage
            {
                ExternalMessageId = telebroadResponse.result.id,
                From = FormatPhoneNumber(telebroadResponse.result.from),
                To = FormatPhoneNumber(telebroadResponse.result.to),
                Text = telebroadResponse.result.text,
                CreatedAt = DateTimeOffset.FromUnixTimeSeconds(long.Parse(telebroadResponse.result.time)).DateTime,
                Direction = telebroadResponse.result.direction,
                ChildId = request.ChildId,
            };

            await _dbContext.Messages.AddAsync(newMessage);
            await _dbContext.SaveChangesAsync();

            return Ok("SMS sent and saved successfully");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to send SMS");
            throw;
        }
    }

    private static string FormatPhoneNumber(string phoneNumber)
    {
        return phoneNumber.StartsWith($"+") ? phoneNumber : $"+1{phoneNumber}";
    }
}