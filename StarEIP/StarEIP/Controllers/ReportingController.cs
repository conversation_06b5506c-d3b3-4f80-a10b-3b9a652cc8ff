﻿using DevExpress.AspNetCore.Reporting.QueryBuilder;
using DevExpress.AspNetCore.Reporting.QueryBuilder.Native.Services;
using DevExpress.AspNetCore.Reporting.ReportDesigner;
using DevExpress.AspNetCore.Reporting.ReportDesigner.Native.Services;
using DevExpress.AspNetCore.Reporting.WebDocumentViewer;
using DevExpress.AspNetCore.Reporting.WebDocumentViewer.Native.Services;
using DevExpress.XtraReports.Web.ReportDesigner.Services;
using Microsoft.AspNetCore.Mvc;

namespace StarEIP.Controllers
{
    [ApiExplorerSettings(IgnoreApi = true)]
    public class CustomWebDocumentViewerController : WebDocumentViewerController
    {
        public CustomWebDocumentViewerController(IWebDocumentViewerMvcControllerService controllerService) : base(controllerService)
        {
        }
    }

    [ApiExplorerSettings(IgnoreApi = true)]
    public class CustomReportDesignerController : ReportDesignerController
    {
        public CustomReportDesignerController(IReportDesignerMvcControllerService controllerService) : base(controllerService)
        {
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> GetDesignerModel([FromForm] string reportUrl, [FromServices] IReportDesignerModelBuilder reportDesignerModelBuilder)
        {
            if (string.IsNullOrEmpty(reportUrl))
            {
                var designerModel = await reportDesignerModelBuilder.Report(new DevExpress.XtraReports.UI.XtraReport()).BuildModelAsync();
                return DesignerModel(designerModel);
            }
            else
            {
                var designerModel = await reportDesignerModelBuilder.Report(reportUrl).BuildModelAsync();
                return DesignerModel(designerModel);
            }
        }
    }


    [ApiExplorerSettings(IgnoreApi = true)]
    public class CustomQueryBuilderController : QueryBuilderController
    {
        public CustomQueryBuilderController(IQueryBuilderMvcControllerService controllerService) : base(controllerService)
        {
        }
    }
}
