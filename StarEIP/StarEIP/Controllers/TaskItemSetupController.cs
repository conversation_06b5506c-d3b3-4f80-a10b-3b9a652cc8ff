using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Tasks;
using StarEIP.Services.Data;
using System.Net;
using TaskStatus = StarEIP.Models.Tasks.TaskStatus;

namespace StarEIP.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TaskItemSetupController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;

        public TaskItemSetupController(StarEipDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        #region Task Status Endpoints

        [HttpGet("taskstatuses")]
        public async Task<IActionResult> GetTaskStatuses()
        {
            try
            {
                var statuses = _dbContext.TaskStatuses;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = new[] { "Id" };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(statuses, loadOptions));
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error retrieving task statuses: {ex.Message}");
            }
        }

        [HttpGet("taskstatuses/{id}")]
        public async Task<IActionResult> GetTaskStatus(int id)
        {
            try
            {
                var status = await _dbContext.TaskStatuses.FindAsync(id);
                if (status == null)
                {
                    return NotFound($"Task status with ID {id} not found");
                }

                return Ok(status);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error retrieving task status: {ex.Message}");
            }
        }

        [HttpPost("taskstatuses")]
        public async Task<IActionResult> CreateTaskStatus([FromBody] TaskStatus status)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _dbContext.TaskStatuses.Add(status);
                await _dbContext.SaveChangesAsync();

                return Ok(status);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error creating task status: {ex.Message}");
            }
        }

        [HttpPut("taskstatuses")]
        public async Task<IActionResult> UpdateTaskStatus([FromBody] TaskStatus status)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var existingStatus = await _dbContext.TaskStatuses.FindAsync(status.Id);
                if (existingStatus == null)
                {
                    return NotFound($"Task status with ID {status.Id} not found");
                }

                existingStatus.Name = status.Name;
                await _dbContext.SaveChangesAsync();

                return Ok(existingStatus);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error updating task status: {ex.Message}");
            }
        }

        [HttpDelete("taskstatuses")]
        public async Task<IActionResult> DeleteTaskStatus(int key)
        {
            try
            {
                var status = await _dbContext.TaskStatuses.FindAsync(key);
                if (status == null)
                {
                    return NotFound($"Task status with ID {key} not found");
                }

                // Check if the status is in use
                var isInUse = await _dbContext.TaskItems.AnyAsync(t => t.TaskStatusId == key);
                if (isInUse)
                {
                    return BadRequest("Cannot delete this status as it is currently in use by one or more tasks");
                }

                _dbContext.TaskStatuses.Remove(status);
                await _dbContext.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error deleting task status: {ex.Message}");
            }
        }

        #endregion

        #region Task Template Endpoints

        [HttpGet("tasktemplates")]
        public async Task<IActionResult> GetTaskTemplates()
        {
            try
            {
                var templates = _dbContext.TaskTemplates;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = new[] { "Id" };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(templates, loadOptions));
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error retrieving task templates: {ex.Message}");
            }
        }

        [HttpGet("tasktemplates/{id}")]
        public async Task<IActionResult> GetTaskTemplate(int id)
        {
            try
            {
                var template = await _dbContext.TaskTemplates.FindAsync(id);
                if (template == null)
                {
                    return NotFound($"Task template with ID {id} not found");
                }

                return Ok(template);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error retrieving task template: {ex.Message}");
            }
        }

        [HttpPost("tasktemplates")]
        public async Task<IActionResult> CreateTaskTemplate([FromBody] TaskTemplate template)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _dbContext.TaskTemplates.Add(template);
                await _dbContext.SaveChangesAsync();

                return Ok(template);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error creating task template: {ex.Message}");
            }
        }

        [HttpPut("tasktemplates")]
        public async Task<IActionResult> UpdateTaskTemplate([FromBody] TaskTemplate template)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var existingTemplate = await _dbContext.TaskTemplates.FindAsync(template.Id);
                if (existingTemplate == null)
                {
                    return NotFound($"Task template with ID {template.Id} not found");
                }

                existingTemplate.Name = template.Name;
                existingTemplate.Description = template.Description;
                existingTemplate.TemplateKey = template.TemplateKey;
                existingTemplate.Category = template.Category;
                existingTemplate.SubCategory = template.SubCategory;

                await _dbContext.SaveChangesAsync();

                return Ok(existingTemplate);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error updating task template: {ex.Message}");
            }
        }

        [HttpDelete("tasktemplates")]
        public async Task<IActionResult> DeleteTaskTemplate(int key)
        {
            try
            {
                var template = await _dbContext.TaskTemplates.FindAsync(key);
                if (template == null)
                {
                    return NotFound($"Task template with ID {key} not found");
                }

                // Check if the template is in use
                var isInUse = await _dbContext.TaskItems.AnyAsync(t => t.TaskTemplateId == key);
                if (isInUse)
                {
                    return BadRequest("Cannot delete this template as it is currently in use by one or more tasks");
                }

                _dbContext.TaskTemplates.Remove(template);
                await _dbContext.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error deleting task template: {ex.Message}");
            }
        }

        #endregion
    }

    // Helper class for DevExtreme data source loading
    public class DataSourceLoadOptions : DataSourceLoadOptionsBase { }
}

