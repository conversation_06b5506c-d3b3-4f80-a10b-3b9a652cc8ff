﻿using Azure.Storage.Blobs;
using Flurl.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using StarEIP.Configuration;
using StarEIP.Models;
using StarEIP.Services;

namespace StarEIP.Controllers
{
    [Route("api/Attachment")]
    [ApiController]
    [Authorize]
    public class AttachmentController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ILogger<AttachmentController> _logger;
        private readonly DocumentStorageService _documentStorageService;
        private readonly TelebroadApiService _telebroadApiService;

        public AttachmentController(
            StarEipDbContext dbContext,
            BlobServiceClient blobServiceClient,
            ILogger<AttachmentController> logger,
            DocumentStorageService documentStorageService,
            TelebroadApiService telebroadApiService)
        {
            _dbContext = dbContext;
            _blobServiceClient = blobServiceClient;
            _logger = logger;
            _documentStorageService = documentStorageService;
            _telebroadApiService = telebroadApiService;
        }

        [HttpGet("{childId}/attachments")]
        public async Task<IActionResult> GetAttachments(int childId)
        {
            var attachments = await (from a in _dbContext.ChildAttachments
                                     join u in _dbContext.Users on a.CreatedBy equals u.Id into userGroup
                                     from u in userGroup.DefaultIfEmpty()
                                     orderby a.CreatedOn descending
                                     where a.ChildId == childId
                                     select new
                                     {
                                         a.Id,
                                         a.FileName,
                                         a.FilePath,
                                         a.FileType,
                                         a.Description,
                                         a.CreatedAt,
                                         CreatedBy = u.UserName,
                                         ShareCount = _dbContext.SharedAttachmentLogs.Count(l => l.AttachmentId == a.Id)
                                     }).ToListAsync();

            return Ok(attachments);
        }

        [HttpGet("{containerName}/{folderName}/{fileName}")]
        public async Task<IActionResult> GetFileContentAsync(string containerName, string folderName, string fileName)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient($"{folderName}/{fileName}");

                if (await blobClient.ExistsAsync())
                {
                    var response = await blobClient.DownloadContentAsync();
                    var fileContent = response.Value.Content.ToArray();
                    var contentType = GetContentType(fileName);
                    return File(fileContent, contentType, fileName);
                }
                else
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get file content");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("{childId}/upload-attachment")]
        public async Task<IActionResult> UploadAttachment(int childId, IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file provided");
            }

            var child = await _dbContext.Children.FindAsync(childId);
            if (child == null)
            {
                return NotFound($"Child with ID {childId} not found");
            }

            try
            {
                using (var uploadFileStream = file.OpenReadStream())
                {
                    await _documentStorageService.SaveDocument(childId, uploadFileStream, file.FileName, file.ContentType);
                }
                return Ok("File uploaded and attachment saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file for child ID {childId}", childId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while uploading the file");
            }
        }

        public record ShareAttachmentRequest(int ContactId, int AttachmentId, string SharedMethod);

        [HttpPost("{childId}/attachments/{attachmentId}/share")]
        public async Task<IActionResult> ShareAttachment(int childId, int attachmentId, [FromBody] ShareAttachmentRequest request)
        {
            try
            {
                var attachment = await _dbContext.ChildAttachments.FindAsync(attachmentId);
                if (attachment == null || attachment.ChildId != childId)
                    return NotFound($"Attachment with ID {attachmentId} not found");

                var contact = await _dbContext.Contacts.FindAsync(request.ContactId);
                if (contact == null)
                    return NotFound($"Contact with ID {request.ContactId} not found");

                if (request.SharedMethod.Equals("fax", StringComparison.CurrentCultureIgnoreCase))
                {
                    var fileName = attachment.FilePath;
                    var faxResult = await SendFax(contact.FaxNumber, fileName);
                    if (!faxResult.Success)
                    {
                        return BadRequest($"Failed to send fax: {faxResult.Message}");
                    }
                }
                //TODO: Implement email sharing logic here


                var log = new SharedAttachmentLog
                {
                    AttachmentId = attachmentId,
                    ContactId = contact.Id,
                    SharedOn = DateTime.Now,
                    SharedMethod = request.SharedMethod
                };

                _dbContext.SharedAttachmentLogs.Add(log);
                await _dbContext.SaveChangesAsync();

                return Ok("Attachment shared successfully");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error sharing attachment with child ID {childId} and attachment ID {attachmentId}",
                    childId, attachmentId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while sharing the attachment");
            }
        }

        [HttpGet("{childId}/attachments/{attachmentId}/share-logs")]
        public async Task<IActionResult> GetShareLogs(int childId, int attachmentId)
        {
            var logs = await (from log in _dbContext.SharedAttachmentLogs
                              where log.AttachmentId == attachmentId
                              join user in _dbContext.Users on log.CreatedBy equals user.Id into userGroup
                              from user in userGroup.DefaultIfEmpty()
                              select new
                              {
                                  log.Id,
                                  log.AttachmentId,
                                  log.ContactId,
                                  log.SharedOn,
                                  log.SharedMethod,
                                  ContactName = $"{user.FirstName} {user.LastName}".Trim(),
                              }).ToListAsync();

            return Ok(logs);
        }

        [HttpDelete("{childId}/attachments/{attachmentId}")]
        public async Task<IActionResult> DeleteAttachment(int childId, int attachmentId)
        {
            var attachment = await _dbContext.ChildAttachments
                .FirstOrDefaultAsync(a => a.ChildId == childId && a.Id == attachmentId);

            if (attachment == null)
                return NotFound($"Attachment with ID {attachmentId} not found");

            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient("child-attachments");
                var blobClient = containerClient.GetBlobClient($"{childId}/{attachment.FileName}");

                await blobClient.DeleteIfExistsAsync();

                _dbContext.ChildAttachments.Remove(attachment);
                await _dbContext.SaveChangesAsync();

                return Ok("Attachment deleted successfully");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error deleting attachment with ID {attachmentId}", attachmentId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while deleting the attachment");
            }
        }


        private async Task<(bool Success, string Message)> SendFax(string toNumber, string fileName)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient("child-attachments");
                var blobClient = containerClient.GetBlobClient(fileName);

                if (!await blobClient.ExistsAsync())
                    return (false, $"File {fileName} not found in the container");

                // Get blob properties to log file size
                var blobProperties = await blobClient.GetPropertiesAsync();
                var fileSizeInBytes = blobProperties.Value.ContentLength;

                // Log the file size
                _logger.LogInformation("File {fileName} has a size of {fileSizeInBytes} bytes ({fileSizeInKb} KB).",
                    fileName, fileSizeInBytes, fileSizeInBytes / 1024.0);

                // Read the PDF file and convert it to base64 string
                var pdfStream = await blobClient.OpenReadAsync();
                string base64 = ConvertToBase64(pdfStream);

                // Use the TelebroadApiService to send the fax
                return await _telebroadApiService.SendFaxAsync(
                    toNumber,
                    base64,
                    Path.GetFileName(blobClient.Name)
                );
            }
            catch (FlurlHttpException e)
            {
                var errorDetails = await e.GetResponseStringAsync();
                _logger.LogError(e, "Error sending fax to {toNumber} with error details: {@errorDetails}", toNumber,
                    errorDetails);
                return (false, $"Failed to send fax: {e.Message}");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error sending fax to {toNumber} with file path {fileName}", toNumber, fileName);
                return (false, $"An error occurred while sending fax: {e.Message}");
            }
        }

        public static string ConvertToBase64(Stream stream)
        {
            byte[] bytes;
            using (var memoryStream = new MemoryStream())
            {
                stream.CopyTo(memoryStream);
                bytes = memoryStream.ToArray();
            }

            return Convert.ToBase64String(bytes);
        }

        private static string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".txt" => "text/plain",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                _ => "application/octet-stream",
            };
        }
    }
}