﻿using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;
using Newtonsoft.Json;
using StarEIP.DTOs;
using StarEIP.Services.Auth;
using StarEIP.Services.Core;

namespace StarEIP.Controllers
{
    [Route("api/users")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.ManageUsers))]
    public class UsersController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<UsersController> _logger;
        private readonly StarEipDbContext _dbContext;
        private readonly IUserService _userService;
        private readonly EmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly string _baseUrl;

        public UsersController(UserManager<ApplicationUser> userManager, ILogger<UsersController> logger,
            StarEipDbContext dbContext,
            IUserService userService,
            EmailService emailService,
            IConfiguration configuration)
        {
            _userManager = userManager;
            _logger = logger;
            _dbContext = dbContext;
            _userService = userService;
            _emailService = emailService;
            _configuration = configuration;
            _baseUrl = _configuration["BaseUrl"];
        }

        [HttpGet("all")]
        [Authorize]
        public ActionResult<List<UserDto>> GetAllUsers()
        {
            try
            {
                var users = _userManager.Users.Select(u => new UserDto
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    UserName = u.UserName
                }).ToList();
                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get all users without policy");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var users = _userService.GetAllUsersQueryable().Select(u => new
                {
                    u.FirstName,
                    u.LastName,
                    u.Email,
                    u.Id,
                    u.UserName,
                    u.EmailConfirmed,
                    u.PhoneNumber,
                    u.PhoneNumberConfirmed,
                    u.TwoFactorEnabled,
                    u.LockoutEnabled,
                    u.LockoutEnd,
                    u.AccessFailedCount
                }).AsQueryable();

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions,
                    key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = [nameof(ApplicationUser.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(users, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get users");
                return StatusCode(500, "Internal server error");
            }
        }

        public record CreateUserRequest(string Email, string FirstName, string LastName, bool SendInviteEmail);
        [HttpPost("invite")]
        public async Task<IActionResult> InviteUser([FromBody] CreateUserRequest request)
        {
            var userExists = await _userManager.FindByEmailAsync(request.Email);
            if (userExists != null)
            {
                return BadRequest("User with this email already exists.");
            }

            var user = new ApplicationUser
            {
                UserName = request.Email,
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName
            };

            var result = await _userManager.CreateAsync(user);
            if (!result.Succeeded)
            {
                return BadRequest(result.Errors);
            }

            if (!request.SendInviteEmail)
            {
                return Ok("User saved.");
            }

            var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            var confirmationLink = $"{_baseUrl}/confirmation?token={Uri.EscapeDataString(token)}&email={Uri.EscapeDataString(user.Email)}";

            try
            {
                var data = new { Name = user.FirstName, confirmationLink };
                await _emailService.SendEmailFromTemplate(user.Email, "Invitation to Join", data);
            }
            catch (Exception e)
            {
                _logger.LogError($"Failed to send invitation email: {e.Message}");
                return BadRequest("Failed to send invitation email.");
            }

            return Ok("User invited successfully. Please check your email.");
        }

        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromForm] int key, [FromForm] string values)
        {
            try
            {
                var user = await _userManager.Users.SingleAsync(r => r.Id == key);
                JsonConvert.PopulateObject(values, user);
                await _userManager.UpdateAsync(user);

                var ipAddress = Request.HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();

                var authLog = new AuthLog
                {
                    UserId = user.Id,
                    action = "Update",
                    IpAddress = ipAddress,
                    UserAgent = userAgent
                };
                await _dbContext.AuthLogs.AddAsync(authLog);
                await _dbContext.SaveChangesAsync();

                return Ok(user);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in updating user");
                throw;
            }
        }

        public record SendResetPasswordRequest(string Email);

        [HttpPost("send-reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] SendResetPasswordRequest request)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    return NotFound();
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                var resetPasswordLink = $"{_baseUrl}/reset-password?token={Uri.EscapeDataString(token)}&email={Uri.EscapeDataString(user.Email)}";

                try
                {
                    dynamic data = new { Name = user.FirstName, resetPasswordLink };
                    await _emailService.SendEmailFromTemplate(user.Email, "Reset Password", data);
                }
                catch (Exception e)
                {
                    _logger.LogError($"Failed to send reset password email: {e.Message}");
                    return BadRequest("Failed to send reset password email.");
                }

                return Ok("Reset password link sent successfully. Please check your email.");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in sending password reset link");
                throw;
            }
        }

        [HttpGet("{userId}/claims")]
        public async Task<IActionResult> GetUserClaims(int userId)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                    return NotFound();

                var claims = await _userService.GetUserClaimsAsync(user);
                return Ok(claims);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in getting user claims");
                throw;
            }
        }

        public record AddClaimRequest(string ClaimValue);

        [HttpPost("{userId}/addClaim")]
        public async Task<IActionResult> AddUserClaim(int userId, [FromBody] AddClaimRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ClaimValue))
                {
                    return BadRequest("ClaimValue is required.");
                }

                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                    return NotFound("User not found.");

                var result = await _userService.AddUserClaimAsync(user, "Permission", request.ClaimValue);
                if (!result.Succeeded)
                    return BadRequest(result.Errors);

                return Ok(user);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in adding user claim");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        public record RemoveClaimRequest(string ClaimValue);

        [HttpPost("{userId}/removeClaim")]
        public async Task<IActionResult> RemoveUserClaim(int userId, [FromBody] RemoveClaimRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ClaimValue))
                {
                    return BadRequest("ClaimValue is required.");
                }

                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                    return NotFound("User not found.");

                var result = await _userService.RemoveUserClaimAsync(user, "Permission", request.ClaimValue);
                if (!result.Succeeded)
                {
                    if (result.Errors.Any(e => e.Code == "ConcurrencyFailure"))
                    {
                        // Handle concurrency conflict
                        return Conflict("The user's claims have been modified. Please refresh and try again.");
                    }

                    return BadRequest(result.Errors);
                }

                return Ok(new { Message = "Claim removed successfully", User = user });
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in removing user claim");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        [HttpGet("claims")]
        public async Task<IList<PermissionDto>> GetUserClaimsAsync(ApplicationUser user)
        {
            var claims = await _userManager.GetClaimsAsync(user);
            var permissions = claims
                .Where(c => c.Type == "Permission")
                .Select(c => new PermissionDto
                {
                    Value = c.Value,
                    FriendlyName = GenerateFriendlyName(c.Value)
                })
                .ToList();

            return permissions;
        }

        private string GenerateFriendlyName(string claimValue)
        {
            if (string.IsNullOrEmpty(claimValue))
            {
                return string.Empty;
            }

            var words = claimValue.Split('_');
            var friendlyName = string.Join(" ", words.Select(w => char.ToUpper(w[0]) + w[1..]));

            return friendlyName;
        }
    }
}