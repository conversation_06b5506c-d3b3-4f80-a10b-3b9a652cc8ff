using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using StarEIP.Models;
using System.Net;
using DevExtreme.AspNet.Data.Helpers;

namespace StarEIP.Controllers
{
    [Route("api/ScStatusSetup")]
    [ApiController]
    public class ScStatusSetupController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;

        public ScStatusSetupController(StarEipDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        [HttpGet("scstatuses")]
        public async Task<IActionResult> GetScStatuses()
        {
            try
            {
                var statuses = _dbContext.ScStatusTypes;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                
                loadOptions.PrimaryKey = new[] { "Id" };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(statuses, loadOptions));
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error retrieving SC statuses: {ex.Message}");
            }
        }

        [HttpPost("scstatuses")]
        public async Task<IActionResult> CreateScStatus([FromForm] ScStatusType scStatus)
        {
            try
            {
                _dbContext.ScStatusTypes.Add(scStatus);
                await _dbContext.SaveChangesAsync();
                return Ok(scStatus);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error creating SC status: {ex.Message}");
            }
        }

        [HttpPut("scstatuses")]
        public async Task<IActionResult> UpdateScStatus([FromForm] int key, [FromForm] ScStatusType scStatus)
        {
            try
            {
                var existingStatus = await _dbContext.ScStatusTypes.FindAsync(key);
                if (existingStatus == null)
                {
                    return NotFound($"SC status with ID {key} not found");
                }

                existingStatus.Name = scStatus.Name;
                existingStatus.SortOrder = scStatus.SortOrder;

                await _dbContext.SaveChangesAsync();
                return Ok(existingStatus);
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error updating SC status: {ex.Message}");
            }
        }

        [HttpDelete("scstatuses")]
        public async Task<IActionResult> DeleteScStatus([FromForm] int key)
        {
            try
            {
                var scStatus = await _dbContext.ScStatusTypes.FindAsync(key);
                if (scStatus == null)
                {
                    return NotFound($"SC status with ID {key} not found");
                }

                // Check if this status is being used by any authorizations
                var isInUse = await _dbContext.Authorizations.AnyAsync(a => a.ScStatus == key);
                if (isInUse)
                {
                    return BadRequest("Cannot delete SC status because it is currently in use by one or more authorizations");
                }

                _dbContext.ScStatusTypes.Remove(scStatus);
                await _dbContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error deleting SC status: {ex.Message}");
            }
        }
    }
}
