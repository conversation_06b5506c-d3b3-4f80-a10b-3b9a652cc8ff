using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using StarEIP.Models.Tasks;
using System.Text.Json;

namespace StarEIP.Controllers
{
    [Route("api/webhook")]
    [ApiController]
    public class WebhookController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;

        public WebhookController(StarEipDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        [HttpPost]
        public async Task<IActionResult> HandleNotification([FromBody] JsonElement notification)
        {
            try
            {
                // Parse the notification to extract email details
                var resourceData = notification.GetProperty("value")[0].GetProperty("resourceData");
                var subject = resourceData.GetProperty("subject").GetString();
                var receivedAt = resourceData.GetProperty("receivedDateTime").GetDateTime();

                // Create a new TaskItem
                var taskItem = new TaskItem
                {
                    Title = subject ?? "New Email Task",
                    CreatedAt = DateTime.UtcNow,
                    DueAt = receivedAt.AddDays(7), // Example: Set due date 7 days from email received date
                    TaskTemplateId = 1, // Replace with a valid TaskTemplateId
                    TaskStatusId = 1  // Replace with a valid TaskStatusId
                };

                _dbContext.TaskItems.Add(taskItem);
                await _dbContext.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}