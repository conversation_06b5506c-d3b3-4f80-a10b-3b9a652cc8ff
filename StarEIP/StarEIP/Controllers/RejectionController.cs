using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data.ResponseModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using StarEIP.Models.Auth;
using StarEIP.Models.Import;
using StarEIP.Models.Tasks;

namespace StarEIP.Controllers
{
    [Route("api/rejections")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.ViewRejections))]
    public class RejectionController(StarEipDbContext dbContext, ILogger<RejectionController> logger) : ControllerBase
    {
        private readonly StarEipDbContext _dbContext = dbContext;
        private readonly ILogger<RejectionController> _logger = logger;

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                // Query for rejected entries
                // Include ChildId via left join on ProgramId and task information
                var query = from rejection in _dbContext.ImportReconciliationEntries
                           join child in _dbContext.Children on rejection.EiNumber equals child.ProgramId.ToString() into childJoin
                           from child in childJoin.DefaultIfEmpty()
                           // Left join with TaskItemLink to get task information
                           join taskLink in _dbContext.TaskItemLinks
                               on new { Table = "Import_ReconciliationEntry", Id = rejection.Id }
                               equals new { Table = taskLink.LinkTable, Id = taskLink.LinkId } into taskLinkJoin
                           from taskLink in taskLinkJoin.DefaultIfEmpty()
                           // Left join with TaskItem to get task details
                           join task in _dbContext.TaskItems on taskLink.TaskItemId equals task.Id into taskJoin
                           from task in taskJoin.DefaultIfEmpty()
                           // Left join with TaskStatus to get status name
                           join taskStatus in _dbContext.TaskStatuses on task.TaskStatusId equals taskStatus.Id into taskStatusJoin
                           from taskStatus in taskStatusJoin.DefaultIfEmpty()
                           select new
                           {
                               rejection.Id,
                               rejection.ChildName,
                               rejection.EiNumber,
                               rejection.Provider,
                               rejection.SessionDate,
                               rejection.ClaimCreated,
                               rejection.Type,
                               rejection.SubType,
                               rejection.BillAmount,
                               rejection.RemittStatus,
                               rejection.RemittDate,
                               rejection.PaymentAmount,
                               rejection.FundingSource,
                               rejection.Discrepancy,
                               rejection.AdjustReasonCodes,
                               rejection.AdjustReasonDescription,
                               rejection.Reconciled,
                               ChildId = child != null ? (int?)child.Id : null,
                               ChildFirstName = child != null ? child.FirstName : null,
                               ChildLastName = child != null ? child.LastName : null,
                               TaskId = task != null ? (int?)task.Id : null,
                               TaskTitle = task != null ? task.Title : null,
                               TaskStatusId = task != null ? (int?)task.TaskStatusId : null,
                               TaskStatusName = taskStatus != null ? taskStatus.Name : null,
                               TaskDueAt = task != null ? task.DueAt : null
                           };

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = [nameof(ImportReconciliationEntry.Id)];
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(query, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving rejections");
                throw;
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var rejection = await _dbContext.ImportReconciliationEntries
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (rejection == null)
                {
                    return NotFound();
                }

                return Ok(rejection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving rejection by ID");
                throw;
            }
        }


    }
}
