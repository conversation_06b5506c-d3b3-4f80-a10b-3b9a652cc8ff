﻿using Microsoft.AspNetCore.Mvc;
using StarEIP.Models;

namespace StarEIP.Controllers
{
    [Route("api/public")]
    [ApiController]
    public class PublicController(ILogger<ChildrenController> logger, StarEipDbContext dbContext) : ControllerBase
    {
        private readonly ILogger<ChildrenController> logger = logger;
        private readonly StarEipDbContext dbContext = dbContext;

        [HttpPost("children/create")]
        public async Task<IActionResult> Create([FromBody] Child child)
        {
            child.Status = "New";
            dbContext.Children.Add(child);
            await dbContext.SaveChangesAsync();
            return Ok();
        }
    }
}
