﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using StarEIP.Models.App;
using StarEIP.Models.Auth;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using StarEIP.DTOs;
using StarEIP.Services.Core;
using StarEIP.Services.Auth;
using static StarEIP.Controllers.AuthController;

namespace StarEIP.Controllers
{
    [ApiController]
    [Route("api/auth")]
    public class AuthController(
        UserManager<ApplicationUser> userManager,
        IUserService userService,
        RoleManager<ApplicationRole> roleManager,
        SignInManager<ApplicationUser> signInManager,
        EmailService emailService,
        ILogger<AuthController> logger,
        IOptions<JwtSettings> jwtSettings,
        IConfiguration configuration,
        StarEipDbContext dbContext) : ControllerBase
    {
        private readonly JwtSettings _jwtSettings = jwtSettings.Value;
        private string _baseUrl = configuration["BaseUrl"];

        [HttpPost("login")]
        public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginRequest loginRequest)
        {
            var user = await userManager.FindByEmailAsync(loginRequest.EmailAddress);
            if (user == null)
            {
                logger.LogWarning("user was not found {EmailAddress}", loginRequest.EmailAddress);
                return Unauthorized("Invalid username or password");
            }

            if (!user.EmailConfirmed)
            {
                return Unauthorized("Email Not Confirmed");
            }

            var results = await signInManager.CheckPasswordSignInAsync(user, loginRequest.Password, true);
            if (!results.Succeeded)
            {
                logger.LogWarning("Failed signing in IsLockedOut: {IsLockedOut}, {EmailAddress}", results.IsLockedOut,
                    loginRequest.EmailAddress);
                return Unauthorized(results.IsLockedOut ? "Account Locked" : "Invalid username or password");
            }


            try
            {
                var ipAddress = Request.HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();

                var authLog = new AuthLog
                {
                    UserId = user.Id,
                    action = "Login",
                    IpAddress = ipAddress,
                    UserAgent = userAgent
                };
                dbContext.AuthLogs.Add(authLog);
                await dbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                logger.LogError("Error saving auth log: {ErrorMessage}", e.Message);
                throw;
            }

            var userClaims = await userManager.GetClaimsAsync(user);
            var token = GenerateJwtToken(user, userClaims);

            var permissions = userClaims
                .Where(c => c.Type == "Permission")
                .Select(c => c.Value)
                .Distinct()
                .ToList();

            var roles = await userManager.GetRolesAsync(user);
            foreach (var roleName in roles)
            {
                var role = await roleManager.FindByNameAsync(roleName);
                var roleClaims = await roleManager.GetClaimsAsync(role);
                permissions.AddRange(roleClaims.Select(c => c.Value));
            }

            return Ok(new AuthResponse
            {
                Token = token,
                Permissions = permissions.Distinct()
            });
        }


        public record ConfirmEmailRequest(string Token, string Email);

        [HttpPost("confirm-email")]
        public async Task<IActionResult> ConfirmEmail(ConfirmEmailRequest confirmEmailRequest)
        {
            var user = await userManager.FindByEmailAsync(confirmEmailRequest.Email);
            if (user == null)
            {
                logger.LogError("User not found {EmailAddress}", confirmEmailRequest.Email);
                return BadRequest("User not found");
            }

            var result = await userManager.ConfirmEmailAsync(user, confirmEmailRequest.Token);

            if (result.Succeeded) return Ok("Email confirmed successfully. You can now login.");

            logger.LogError("Failed confirming email {EmailAddress}", confirmEmailRequest.Token);
            return BadRequest("Failed to confirm email");
        }

        public record SetPasswordRequest(string EmailAddress, string Password);

        [HttpPost("set-password")]
        public async Task<IActionResult> SetPassword([FromBody] SetPasswordRequest setPasswordRequest)
        {
            var user = await userManager.FindByEmailAsync(setPasswordRequest.EmailAddress);

            if (user == null)
            {
                logger.LogError("User not found {EmailAddress}", setPasswordRequest.EmailAddress);
                return BadRequest("User not found");
            }

            if (await userManager.HasPasswordAsync(user))
            {
                return BadRequest("User already has a password");
            }

            var result = await userManager.AddPasswordAsync(user, setPasswordRequest.Password);

            if (result.Succeeded) return Ok("Password set successfully. You can now login.");

            logger.LogError("Failed setting password {EmailAddress}", setPasswordRequest.EmailAddress);
            return BadRequest(result.Errors);
        }

        public record SetNewPasswordRequest(string EmailAddress, string token, string newPassword);

        [HttpPost("set-new-password")]
        public async Task<IActionResult> SetNewPassword(SetNewPasswordRequest setNewPasswordRequest)
        {
            try
            {
                var user = await userManager.FindByEmailAsync(setNewPasswordRequest.EmailAddress);

                if (user == null)
                {
                    logger.LogError("User not found {EmailAddress}", setNewPasswordRequest.EmailAddress);
                    return BadRequest("User not found");
                }

                var result = await userManager.ResetPasswordAsync(user, setNewPasswordRequest.token,
                    setNewPasswordRequest.newPassword);
                user.EmailConfirmed = true;
                await userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    return Ok("Password reset successfully. You can now login.");
                }

                logger.LogError("Failed resetting password {EmailAddress}", setNewPasswordRequest.EmailAddress);
                return BadRequest(result.Errors);
            }
            catch (Exception e)
            {
                logger.LogError("Error during password reset: {ErrorMessage}", e.Message);
                throw;
            }
        }

        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetUserProfile()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userId == null)
                return Unauthorized("User not authenticated");

            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
                return Unauthorized("User not found");
            var userClaims = await userManager.GetClaimsAsync(user);

            var permissions = userClaims
                .Where(c => c.Type == "Permission")
                .Select(c => c.Value)
                .Distinct()
                .ToList();

            var token = GenerateJwtToken(user, userClaims);

            var roles = await userManager.GetRolesAsync(user);
            foreach (var roleName in roles)
            {
                var role = await roleManager.FindByNameAsync(roleName);
                var roleClaims = await roleManager.GetClaimsAsync(role);
                permissions.AddRange(roleClaims.Select(c => c.Value));
            }

            return Ok(new
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Permissions = permissions.Distinct(),
                Token = token
            });
        }

        [HttpPost("create_default_user")]
        public async Task<IActionResult> CreateDefaultUser()
        {
            await dbContext.Database.MigrateAsync();

            var defaultUsers = new[]
            {
                new { Email = "<EMAIL>", Password = "$tarE1P", FirstName = "Joel", LastName = "Test" },
                new { Email = "<EMAIL>", Password = "$tarE1P", FirstName = "Alek", LastName = "Test" }
            };

            var createdUsers = new List<ApplicationUser>();

            foreach (var defaultUser in defaultUsers)
            {
                var existingUser = await userManager.FindByEmailAsync(defaultUser.Email);
                if (existingUser != null)
                {
                    continue;
                }

                var user = new ApplicationUser
                {
                    Email = defaultUser.Email,
                    AccessFailedCount = 0,
                    EmailConfirmed = true,
                    LockoutEnabled = true,
                    UserName = defaultUser.Email,
                    TwoFactorEnabled = false,
                    FirstName = defaultUser.FirstName,
                    LastName = defaultUser.LastName,
                };

                var result = await userManager.CreateAsync(user, defaultUser.Password);
                if (result.Succeeded)
                {
                    createdUsers.Add(user);
                }
                else
                {
                    return BadRequest(
                        $"Failed to create user {defaultUser.Email}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }

            if (createdUsers.Count == 0)
            {
                return BadRequest("Default users already exist");
            }

            return Ok(createdUsers);
        }

        [HttpPost("assign_roles_and_permissions")]
        public async Task<IActionResult> AssignRolesAndPermissions()
        {
            var adminUser = await userManager.FindByEmailAsync("<EMAIL>");
            if (adminUser != null)
            {
                await userManager.AddToRolesAsync(adminUser, ["Admin", "Manager"]);
            }

            var managerUser = await userManager.FindByEmailAsync("<EMAIL>");
            if (managerUser != null)
            {
                await userManager.AddToRoleAsync(managerUser, "Manager");
            }

            var adminRole = await roleManager.FindByNameAsync("Admin");
            var managerRole = await roleManager.FindByNameAsync("Manager");

            var allPermissions = new List<Claim>
            {
                new Claim("Permission", UserPermission.ViewDashboard),
                new Claim("Permission", UserPermission.ManageUsers),
                new Claim("Permission", UserPermission.ViewChildren),
                new Claim("Permission", UserPermission.ViewAuthorizations),
                new Claim("Permission", UserPermission.ViewFaxes),
                new Claim("Permission", UserPermission.ViewEmailTemplates),
                new Claim("Permission", UserPermission.ViewPhysicians),
                new Claim("Permission", UserPermission.ViewAllAuth),
                new Claim("Permission", UserPermission.ViewReports),
                new Claim("Permission", UserPermission.ViewAuditLogs),
            };

            var adminOnlyPermissions = new List<Claim>
            {
                new Claim("Permission", UserPermission.AllowManageAuthorization),
                new Claim("Permission", UserPermission.AllowReportDesigner),
            };

            // Assign all permissions to the admin role
            foreach (var claim in allPermissions.Concat(adminOnlyPermissions))
            {
                var existingClaims = await roleManager.GetClaimsAsync(adminRole);
                if (!existingClaims.Any(c => c.Type == claim.Type && c.Value == claim.Value))
                {
                    await roleManager.AddClaimAsync(adminRole, claim);
                }
            }

            // Assign all permissions except admin-only ones to the manager role
            foreach (var claim in allPermissions)
            {
                var existingClaims = await roleManager.GetClaimsAsync(managerRole);
                if (!existingClaims.Any(c => c.Type == claim.Type && c.Value == claim.Value))
                {
                    await roleManager.AddClaimAsync(managerRole, claim);
                }
            }

            return Ok("Roles and permissions assigned successfully");
        }

        [HttpGet("auth-log/{userId}")]
        public async Task<IActionResult> GetAuthLogsForUser(int userId)
        {
            var authLogs = await dbContext.AuthLogs
                .Where(al => al.UserId == userId)
                .ToListAsync();

            return Ok(authLogs);
        }

        [HttpGet("permissions")]
        public async Task<IActionResult> GetAllPermissions()
        {
            var properties = typeof(UserPermission).GetProperties();
            var permissions = properties
                .Where(p => !new string[] { nameof(UserPermission.PermissionClaimName) }.Contains(p.Name))
                .Select(permissionProperty => new PermissionDto
                {
                    Value = permissionProperty.Name,
                    FriendlyName = GenerateFriendlyName(permissionProperty.Name)
                })
                .ToList();

            return Ok(permissions);
        }

        private string GenerateJwtToken(ApplicationUser user, IEnumerable<Claim> claims)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);

            var tokenClaims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Email, user.Email ?? ""),
                new Claim("JwtGuid", Guid.NewGuid().ToString())
            };

            tokenClaims.AddRange(claims);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(tokenClaims),
                Issuer = _jwtSettings.TokenIssuer,
                Audience = _jwtSettings.TokenAudience,
                Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpirationMinutes),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature)
            };
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateFriendlyName(string claimValue)
        {
            if (string.IsNullOrEmpty(claimValue))
            {
                return string.Empty;
            }

            var words = claimValue.Split('_');
            var friendlyName = string.Join(" ", words.Select(w => char.ToUpper(w[0]) + w.Substring(1)));

            return friendlyName;
        }
    }

    public record LoginRequest(string EmailAddress, string Password);

    public struct AuthResponse
    {
        public string Token { get; set; }
        public IEnumerable<string> Permissions { get; set; }
    }
}
