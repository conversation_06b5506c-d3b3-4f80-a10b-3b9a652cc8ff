using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using Microsoft.AspNetCore.Authorization;

namespace StarEIP.Controllers
{
    [Route("api/child-reconciliation")]
    [ApiController]
    [Authorize]
    public class ChildReconciliationController : ControllerBase
    {
        private readonly ILogger<ChildReconciliationController> _logger;
        private readonly StarEipDbContext _dbContext;

        public ChildReconciliationController(ILogger<ChildReconciliationController> logger, StarEipDbContext dbContext)
        {
            _logger = logger;
            _dbContext = dbContext;
        }

        [HttpGet]
        public async Task<IActionResult> Get(string? serviceCoordinator = null)
        {
            try
            {
                var childReconciliations = _dbContext.ChildReconciliations.AsQueryable();

                // Filter by service coordinator if provided
                if (!string.IsNullOrEmpty(serviceCoordinator))
                {
                    // Get child IDs that have the specified service coordinator
                    var childIdsWithServiceCoordinator = await _dbContext.ImportChildInfoAuthorizations
                        .Where(a => (a.ServiceCoordinatorFirstName + " " + a.ServiceCoordinatorLastName) == serviceCoordinator)
                        .Select(a => a.EiChildId)
                        .Distinct()
                        .ToListAsync();

                    childReconciliations = childReconciliations.Where(cr => childIdsWithServiceCoordinator.Contains(cr.ChildId));
                }

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                _dbContext.ChangeTracker.LazyLoadingEnabled = true;
                _dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = new[] { nameof(ChildReconciliation.ChildId) };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(childReconciliations, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get child reconciliation data");
                throw;
            }
        }

        [HttpGet("service-coordinators")]
        public async Task<IActionResult> GetServiceCoordinators()
        {
            try
            {
                var serviceCoordinators = await _dbContext.ImportChildInfoAuthorizations
                    .Where(a => a.ServiceCoordinatorCompany == "Star EIP LLC" &&
                               !string.IsNullOrEmpty(a.ServiceCoordinatorFirstName) &&
                               !string.IsNullOrEmpty(a.ServiceCoordinatorLastName))
                    .Select(a => a.ServiceCoordinatorFirstName + " " + a.ServiceCoordinatorLastName)
                    .Distinct()
                    .OrderBy(name => name)
                    .ToListAsync();

                return Ok(serviceCoordinators);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get service coordinators");
                throw;
            }
        }

        [HttpGet("{childId}")]
        public async Task<ActionResult<ChildReconciliation>> GetById(string childId)
        {
            try
            {
                var childReconciliation = await _dbContext.ChildReconciliations
                    .FirstOrDefaultAsync(c => c.ChildId == childId);

                if (childReconciliation == null)
                    return NotFound();

                return Ok(childReconciliation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get child reconciliation by id");
                throw;
            }
        }
    }
}
