﻿using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using StarEIP.Models;
using StarEIP.Models.Auth;

namespace StarEIP.Controllers
{
    [Route("api/Contacts")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.AllowManageContacts))]
    public class ContactsController : ControllerBase
    {
        private readonly StarEipDbContext dbContext;
        private readonly ILogger<ContactsController> logger;

        public ContactsController(StarEipDbContext starEipDbContext, ILogger<ContactsController> logger)
        {
            dbContext = starEipDbContext;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var contacts = dbContext.Contacts;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = [nameof(Contact.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(contacts, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get users");
                throw;
            }
        }

        [HttpGet("list")]
        public async Task<IActionResult> GetContacts()
        {
            var contacts = await dbContext.Contacts.ToListAsync();
            return Ok(contacts);
        }

        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromForm] int key, [FromForm] string values)
        {
            var staff = await dbContext.Contacts.SingleAsync(r => r.Id == key);
            JsonConvert.PopulateObject(values, staff);
            await dbContext.SaveChangesAsync();
            return Ok(staff);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromForm] string values)
        {
            var contact = new Contact();
            JsonConvert.PopulateObject(values, contact);

            if (string.IsNullOrEmpty(contact.Name))
            {
                return BadRequest("EmailTemplate Name is required.");
            }

            await dbContext.Contacts.AddAsync(contact);
            await dbContext.SaveChangesAsync();
            return Ok(contact);
        }
    }
}
