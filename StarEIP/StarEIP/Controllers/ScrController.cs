﻿using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using StarEIP.Models.SCR;

namespace StarEIP.Controllers
{
    [Route("api/scr")]
    [ApiController]
    public class ScrController : ControllerBase
    {
        private readonly StarEipDbContext dbContext;
        private readonly ILogger<ScrController> logger;

        public ScrController(StarEipDbContext dbContext, ILogger<ScrController> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var openOrders = dbContext.ScrForms;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                dbContext.ChangeTracker.LazyLoadingEnabled = true;
                dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = [nameof(Staff.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(openOrders, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get staff");
                throw;
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> Create(int staffId)
        {
            try
            {
                var scrForm = new ScrForm
                {
                    StaffId = staffId,
                    CreatedData = DateTime.Now
                };
                scrForm.FormLinks =
                [
                    new ScrFormLink
                    {
                        GeneratedAt = DateTime.Now,
                        ExpiryDate = DateTime.Now.AddHours(24),
                        UniqueCode = Guid.NewGuid().ToString()
                    },
                ];
                dbContext.ScrForms.Add(scrForm);
                await dbContext.SaveChangesAsync();
                return Ok(scrForm);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in create scr form");
                throw;
            }
        }

        [HttpGet("{key}")]
        public async Task<IActionResult> GetScr(string key)
        {
            if (key != "test")
            {
                return NotFound();
            }
            return Ok();
        }
    }
}
