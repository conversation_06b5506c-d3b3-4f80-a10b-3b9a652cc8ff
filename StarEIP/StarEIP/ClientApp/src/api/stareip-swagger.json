{"openapi": "3.0.4", "info": {"title": "StarEIP Web API", "version": "v1"}, "paths": {"/api/Attachment/{childId}/attachments": {"get": {"tags": ["Attachment"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Attachment/{childId}/attachments/{attachmentId}": {"delete": {"tags": ["Attachment"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "attachmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Attachment/{childId}/attachments/{attachmentId}/share": {"post": {"tags": ["Attachment"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "attachmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AttachmentController+ShareAttachmentRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AttachmentController+ShareAttachmentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AttachmentController+ShareAttachmentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AttachmentController+ShareAttachmentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Attachment/{childId}/attachments/{attachmentId}/share-logs": {"get": {"tags": ["Attachment"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "attachmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Attachment/{childId}/upload-attachment": {"post": {"tags": ["Attachment"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Attachment/{containerName}/{folderName}/{fileName}": {"get": {"tags": ["Attachment"], "parameters": [{"name": "containerName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "folderName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/auth/assign_roles_and_permissions": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/auth-log/{userId}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/auth/confirm-email": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+ConfirmEmailRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+ConfirmEmailRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+ConfirmEmailRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+ConfirmEmailRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/create_default_user": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.LoginRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthResponse"}}}}}}}, "/api/auth/permissions": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/profile": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/set-new-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetNewPasswordRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetNewPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetNewPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetNewPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/set-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetPasswordRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthController+SetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/authorizations": {"get": {"tags": ["Authorization"], "parameters": [{"name": "childId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/{id}": {"get": {"tags": ["Authorization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/{id}/status": {"post": {"tags": ["Authorization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthorizationController+AuthorizationStatusUpdateRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthorizationController+AuthorizationStatusUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthorizationController+AuthorizationStatusUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.AuthorizationController+AuthorizationStatusUpdateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/child/{childId}": {"get": {"tags": ["Authorization"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/create": {"post": {"tags": ["Authorization"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/Status": {"get": {"tags": ["Authorization"], "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/statuses": {"get": {"tags": ["Authorization"], "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/Update": {"put": {"tags": ["Authorization"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/authorizations/update/{id}": {"post": {"tags": ["Authorization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/children": {"get": {"tags": ["Children"], "responses": {"200": {"description": "OK"}}}}, "/api/children/{childId}": {"put": {"tags": ["Children"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildUpdateDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/children/{id}": {"get": {"tags": ["Children"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}}}}}}}, "/api/children/create": {"post": {"tags": ["Children"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}}}}}}, "/api/children/Delete": {"delete": {"tags": ["Children"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}}}, "encoding": {"key": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/children/map-ei-number": {"post": {"tags": ["Children"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+MapEiNumberToChildRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+MapEiNumberToChildRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+MapEiNumberToChildRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+MapEiNumberToChildRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/children/status": {"post": {"tags": ["Children"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildStatusUpdateRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildStatusUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildStatusUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.ChildrenController+ChildStatusUpdateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}}}}}}}, "/api/children/Status": {"get": {"tags": ["Children"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.ChildStatus"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.ChildStatus"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.ChildStatus"}}}}}}}}, "/api/children/Update": {"put": {"tags": ["Children"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}}}}}}, "/api/children/update/{id}": {"post": {"tags": ["Children"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}}}}}}, "/api/Contacts": {"get": {"tags": ["Contacts"], "responses": {"200": {"description": "OK"}}}}, "/api/Contacts/Create": {"post": {"tags": ["Contacts"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"values": {"type": "string"}}}, "encoding": {"values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Contacts/list": {"get": {"tags": ["Contacts"], "responses": {"200": {"description": "OK"}}}}, "/api/Contacts/Update": {"put": {"tags": ["Contacts"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplates": {"get": {"tags": ["EmailTemplate"], "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplates/Create": {"post": {"tags": ["EmailTemplate"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"values": {"type": "string"}}}, "encoding": {"values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplates/Update": {"put": {"tags": ["EmailTemplate"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "string"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/faxes": {"get": {"tags": ["Faxes"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Faxes"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.FaxChild"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.FaxChild"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.FaxChild"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.FaxChild"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/faxes/{faxId}/pdf": {"get": {"tags": ["Faxes"], "parameters": [{"name": "faxId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/faxes/LinkPhysician": {"post": {"tags": ["Faxes"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.FaxesController+LinkPhysicianRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.FaxesController+LinkPhysicianRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.FaxesController+LinkPhysicianRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.FaxesController+LinkPhysicianRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/FileImport/ImportTypes": {"get": {"tags": ["FileImport"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ImportType"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ImportType"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ImportType"}}}}}}}}, "/api/FileImport/PsChildren": {"post": {"tags": ["FileImport"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ChildImportModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ChildImportModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ChildImportModel"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Import.ChildImportModel"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/FileImport/UploadFile/{importKey}": {"post": {"tags": ["FileImport"], "parameters": [{"name": "importKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/notes/{entityType}/{entityId}": {"get": {"tags": ["Notes"], "parameters": [{"name": "entityType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Notes"], "parameters": [{"name": "entityType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/notes/{id}": {"delete": {"tags": ["Notes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/notes/child/{childId}": {"get": {"tags": ["Notes"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Notes"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.NotesController+NoteRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/physicians": {"get": {"tags": ["Physician"], "responses": {"200": {"description": "OK"}}}}, "/api/physicians/{id}": {"get": {"tags": ["Physician"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/physicians/Create": {"post": {"tags": ["Physician"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"values": {"type": "string"}}}, "encoding": {"values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/physicians/Update": {"put": {"tags": ["Physician"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/public/children/create": {"post": {"tags": ["Public"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/rejections": {"get": {"tags": ["Rejection"], "responses": {"200": {"description": "OK"}}}}, "/api/rejections/{id}": {"get": {"tags": ["Rejection"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/reports": {"get": {"tags": ["Reports"], "responses": {"200": {"description": "OK"}}}}, "/api/reports/{id}/updateFields": {"put": {"tags": ["Reports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UpdateReportFieldsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UpdateReportFieldsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UpdateReportFieldsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UpdateReportFieldsDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/reports/{name}": {"get": {"tags": ["Reports"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/reports/child-details": {"get": {"tags": ["Reports"], "parameters": [{"name": "isAuthDetail", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles": {"get": {"tags": ["Roles"], "responses": {"200": {"description": "OK"}}}}, "/api/roles/{id}": {"get": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles/{roleId}": {"delete": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles/create": {"post": {"tags": ["Roles"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.RolesController+CreateRoleRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.RolesController+CreateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.RolesController+CreateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.RolesController+CreateRoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/scr": {"get": {"tags": ["Scr"], "responses": {"200": {"description": "OK"}}}}, "/api/scr/{key}": {"get": {"tags": ["Scr"], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/scr/Create": {"post": {"tags": ["Scr"], "parameters": [{"name": "staffId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/sms/messages/{childId}": {"get": {"tags": ["Sms"], "parameters": [{"name": "childId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/sms/send": {"post": {"tags": ["Sms"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.SmsController+SendSmsRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.SmsController+SendSmsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.SmsController+SendSmsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.SmsController+SendSmsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Staff": {"get": {"tags": ["Staff"], "responses": {"200": {"description": "OK"}}}}, "/api/Staff/Create": {"post": {"tags": ["Staff"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Staff/Delete": {"delete": {"tags": ["Staff"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}}}, "encoding": {"key": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Staff/invite": {"post": {"tags": ["Staff"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Staff"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Staff"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Staff"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Staff"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Staff/Update": {"put": {"tags": ["Staff"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TaskItems": {"get": {"tags": ["TaskItems"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["TaskItems"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+CreateTaskRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+CreateTaskRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+CreateTaskRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+CreateTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}}}}}}, "/api/TaskItems/{taskId}": {"put": {"tags": ["TaskItems"], "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+SingleUpdateTaskRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+SingleUpdateTaskRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+SingleUpdateTaskRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+SingleUpdateTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}}}}}, "get": {"tags": ["TaskItems"], "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FullTaskDetailDto"}}}}}}}, "/api/TaskItems/bulk-update": {"post": {"tags": ["TaskItems"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+BulkUpdateTasksRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+BulkUpdateTasksRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+BulkUpdateTasksRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+BulkUpdateTasksRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.App.TaskDetailsDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.App.TaskDetailsDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.App.TaskDetailsDto"}}}}}}}}, "/api/TaskItems/statuses": {"get": {"tags": ["TaskItems"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskStatus"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskStatus"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskStatus"}}}}}}}}, "/api/test": {"post": {"tags": ["Test"], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Test"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "filterEmail", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/addClaim": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+AddClaimRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+AddClaimRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+AddClaimRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+AddClaimRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/claims": {"get": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/removeClaim": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+RemoveClaimRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+RemoveClaimRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+RemoveClaimRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+RemoveClaimRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/all": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.DTOs.UserDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.DTOs.UserDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.DTOs.UserDto"}}}}}}}}, "/api/users/claims": {"get": {"tags": ["Users"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Auth.ApplicationUser"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Auth.ApplicationUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Auth.ApplicationUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Models.Auth.ApplicationUser"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.DTOs.PermissionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.DTOs.PermissionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.DTOs.PermissionDto"}}}}}}}}, "/api/users/invite": {"post": {"tags": ["Users"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+CreateUserRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+CreateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/send-reset-password": {"post": {"tags": ["Users"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+SendResetPasswordRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+SendResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+SendResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StarEIP.Controllers.UsersController+SendResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/Update": {"put": {"tags": ["Users"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"key": {"type": "integer", "format": "int32"}, "values": {"type": "string"}}}, "encoding": {"key": {"style": "form"}, "values": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/webhook": {"post": {"tags": ["Webhook"], "requestBody": {"content": {"application/json-patch+json": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"StarEIP.Controllers.AttachmentController+ShareAttachmentRequest": {"type": "object", "properties": {"contactId": {"type": "integer", "format": "int32"}, "attachmentId": {"type": "integer", "format": "int32"}, "sharedMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.AuthController+ConfirmEmailRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.AuthController+SetNewPasswordRequest": {"type": "object", "properties": {"emailAddress": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.AuthController+SetPasswordRequest": {"type": "object", "properties": {"emailAddress": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.AuthResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.AuthorizationController+AuthorizationStatusUpdateRequest": {"type": "object", "properties": {"authorizationId": {"type": "integer", "format": "int32"}, "statusId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.ChildrenController+ChildStatusUpdateRequest": {"type": "object", "properties": {"childId": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.ChildrenController+ChildUpdateDto": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"type": "string", "nullable": true}, "primaryLanguage": {"type": "string", "nullable": true}, "reasonForReferral": {"type": "string", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "parentPhoneNumber": {"type": "string", "nullable": true}, "parentEmail": {"type": "string", "nullable": true}, "fullAddress": {"type": "string", "nullable": true}, "referringPhysicianName": {"type": "string", "nullable": true}, "physicianPhoneNumber": {"type": "string", "nullable": true}, "physicianEmailAddress": {"type": "string", "nullable": true}, "referralMethod": {"type": "string", "nullable": true}, "referringPhysicianId": {"type": "integer", "format": "int32", "nullable": true}, "programId": {"type": "integer", "format": "int32", "nullable": true}, "providerSoftChildId": {"type": "integer", "format": "int32", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.ChildrenController+MapEiNumberToChildRequest": {"type": "object", "properties": {"eiNumber": {"type": "string", "nullable": true}, "childId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StarEIP.Controllers.FaxesController+LinkPhysicianRequest": {"type": "object", "properties": {"faxId": {"type": "integer", "format": "int32"}, "physicianId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StarEIP.Controllers.LoginRequest": {"type": "object", "properties": {"emailAddress": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.NotesController+NoteRequest": {"type": "object", "properties": {"note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.RolesController+CreateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.SmsController+SendSmsRequest": {"type": "object", "properties": {"to": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "childId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+BulkUpdateTasksRequest": {"type": "object", "properties": {"taskIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "assignedToUserId": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "taskStatusId": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "dueAt": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "unlinkAllLinks": {"type": "boolean", "nullable": true}, "linkedItems": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+TaskLinkItem"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+CreateTaskRequest": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "taskTemplateId": {"type": "integer", "format": "int32", "nullable": true}, "childId": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "assignedToUserId": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "taskStatusId": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "dueAt": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "unlinkAllLinks": {"type": "boolean", "nullable": true, "readOnly": true}, "linkedItems": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+TaskLinkItem"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"update": {"type": "boolean"}, "value": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"update": {"type": "boolean"}, "value": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+FullTaskDetailDto": {"type": "object", "properties": {"taskDetailsDto": {"$ref": "#/components/schemas/StarEIP.Models.App.TaskDetailsDto"}, "childDetailsDto": {"$ref": "#/components/schemas/StarEIP.Models.ChildDetailsDto"}, "taskItemLinks": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItemLink"}, "nullable": true}, "emails": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItemEmail"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+SingleUpdateTaskRequest": {"type": "object", "properties": {"assignedToUserId": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "taskStatusId": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "dueAt": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+FieldUpdate`1[[System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "unlinkAllLinks": {"type": "boolean", "nullable": true}, "linkedItems": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Controllers.TaskItemsController+TaskLinkItem"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.TaskItemsController+TaskLinkItem": {"type": "object", "properties": {"tableName": {"type": "string", "nullable": true}, "itemId": {"type": "integer", "format": "int32"}, "linkType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.UpdateReportFieldsDto": {"type": "object", "properties": {"showOnChildDetails": {"type": "boolean", "nullable": true}, "showOnAuthDetails": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.UsersController+AddClaimRequest": {"type": "object", "properties": {"claimValue": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.UsersController+CreateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "sendInviteEmail": {"type": "boolean"}}, "additionalProperties": false}, "StarEIP.Controllers.UsersController+RemoveClaimRequest": {"type": "object", "properties": {"claimValue": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Controllers.UsersController+SendResetPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.DTOs.PermissionDto": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "friendlyName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.DTOs.UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.App.TaskDetailsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "dueAt": {"type": "string", "format": "date-time", "nullable": true}, "assignedToUserId": {"type": "integer", "format": "int32", "nullable": true}, "assignedToUser": {"$ref": "#/components/schemas/StarEIP.DTOs.UserDto"}, "childId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Auth.ApplicationUser": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "passwordHash": {"type": "string", "nullable": true}, "securityStamp": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Authorization": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "childId": {"type": "integer", "format": "int32"}, "statusId": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "authNumber": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "units": {"type": "integer", "format": "int32", "nullable": true}, "child": {"$ref": "#/components/schemas/StarEIP.Models.Child"}, "user": {"$ref": "#/components/schemas/StarEIP.Models.Auth.ApplicationUser"}, "status": {"$ref": "#/components/schemas/StarEIP.Models.AuthorizationStatus"}}, "additionalProperties": false}, "StarEIP.Models.AuthorizationStatus": {"required": ["name"], "type": "object", "properties": {"id": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "name": {"maxLength": 255, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "StarEIP.Models.Child": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "updatedOn": {"type": "string", "format": "date-time"}, "deletedBy": {"type": "integer", "format": "int32", "nullable": true}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusLastUpdated": {"type": "string", "format": "date-time", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "patientFullName": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "primaryLanguage": {"type": "string", "nullable": true}, "reasonForReferral": {"type": "string", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "parentPhoneNumber": {"type": "string", "nullable": true}, "parentEmail": {"type": "string", "nullable": true}, "fullAddress": {"type": "string", "nullable": true}, "referringPhysicianName": {"type": "string", "nullable": true}, "physicianPhoneNumber": {"type": "string", "nullable": true}, "physicianEmailAddress": {"type": "string", "nullable": true}, "referralMethod": {"type": "string", "nullable": true}, "referringPhysicianId": {"type": "integer", "format": "int32", "nullable": true}, "referringPhysician": {"$ref": "#/components/schemas/StarEIP.Models.Physician"}, "programId": {"type": "integer", "format": "int32", "nullable": true}, "providerSoftChildId": {"type": "integer", "format": "int32", "nullable": true}, "authorizations": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Authorization"}, "nullable": true}, "childAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.ChildAttachment"}, "nullable": true}, "notes": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Notes"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.ChildAttachment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "updatedOn": {"type": "string", "format": "date-time"}, "deletedBy": {"type": "integer", "format": "int32", "nullable": true}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "childId": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "child": {"$ref": "#/components/schemas/StarEIP.Models.Child"}}, "additionalProperties": false}, "StarEIP.Models.ChildDetailsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "patientName": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"type": "string", "nullable": true}, "primaryLanguage": {"type": "string", "nullable": true}, "reasonForReferral": {"type": "string", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "parentPhoneNumber": {"type": "string", "nullable": true}, "parentEmail": {"type": "string", "nullable": true}, "fullAddress": {"type": "string", "nullable": true}, "referringPhysicianName": {"type": "string", "nullable": true}, "physicianPhoneNumber": {"type": "string", "nullable": true}, "physicianEmailAddress": {"type": "string", "nullable": true}, "referralMethod": {"type": "string", "nullable": true}, "referringPhysicianId": {"type": "integer", "format": "int32", "nullable": true}, "programId": {"type": "integer", "format": "int32", "nullable": true}, "providerSoftChildId": {"type": "integer", "format": "int32", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusLastUpdated": {"type": "string", "format": "date-time", "nullable": true}, "notesCount": {"type": "integer", "format": "int32"}, "authorizationsCount": {"type": "integer", "format": "int32"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedOn": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.ChildStatus": {"required": ["sortOrder"], "type": "object", "properties": {"status": {"type": "string", "nullable": true}, "statusColor": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StarEIP.Models.FaxChild": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "faxId": {"type": "integer", "format": "int32"}, "childId": {"type": "integer", "format": "int32"}, "isNewReferral": {"type": "boolean"}}, "additionalProperties": false}, "StarEIP.Models.Import.ChildImportModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "programId": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "stage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Import.ImportType": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "importKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Notes": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "updatedOn": {"type": "string", "format": "date-time"}, "deletedBy": {"type": "integer", "format": "int32", "nullable": true}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "note": {"type": "string", "nullable": true}, "childId": {"type": "integer", "format": "int32", "nullable": true}, "child": {"$ref": "#/components/schemas/StarEIP.Models.Child"}, "entityType": {"type": "string", "nullable": true}, "entityId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Physician": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "streetAddress": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "facilityName": {"type": "string", "nullable": true}, "referralSourceType": {"$ref": "#/components/schemas/StarEIP.Models.ReferralSourceType"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Child"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.ReferralSourceType": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "StarEIP.Models.SCR.ScrAddressHistory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "fromDate": {"type": "string", "format": "date-time"}, "toDate": {"type": "string", "format": "date-time"}, "scrFormId": {"type": "integer", "format": "int32"}, "scrForm": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrForm"}}, "additionalProperties": false}, "StarEIP.Models.SCR.ScrForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "submissionDate": {"type": "string", "format": "date-time"}, "expiryDate": {"type": "string", "format": "date-time"}, "staffId": {"type": "integer", "format": "int32"}, "createdData": {"type": "string", "format": "date-time"}, "staff": {"$ref": "#/components/schemas/StarEIP.Models.Staff"}, "householdMembers": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrHouseholdMember"}, "nullable": true}, "addressHistories": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrAddressHistory"}, "nullable": true}, "formLinks": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrFormLink"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.SCR.ScrFormLink": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "uniqueCode": {"type": "string", "nullable": true}, "scrFormId": {"type": "integer", "format": "int32"}, "generatedAt": {"type": "string", "format": "date-time"}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "isClicked": {"type": "boolean"}, "scrForm": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrForm"}}, "additionalProperties": false}, "StarEIP.Models.SCR.ScrHouseholdMember": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "age": {"type": "integer", "format": "int32"}, "scrFormId": {"type": "integer", "format": "int32"}, "scrForm": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrForm"}}, "additionalProperties": false}, "StarEIP.Models.Staff": {"required": ["email", "firstName", "lastName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "scrForms": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.SCR.ScrForm"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Tasks.EmailLinkType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "StarEIP.Models.Tasks.EmailMessage": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "graphMessageId": {"type": "string", "nullable": true}, "internetMessageId": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "senderEmail": {"type": "string", "nullable": true}, "receivedAt": {"type": "string", "format": "date-time"}, "webLink": {"type": "string", "nullable": true}, "linkedTasks": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItemEmail"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Tasks.TaskItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "taskTemplateId": {"type": "integer", "format": "int32"}, "taskStatusId": {"type": "integer", "format": "int32"}, "assignedToUserId": {"type": "integer", "format": "int32", "nullable": true}, "childId": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "dueAt": {"type": "string", "format": "date-time", "nullable": true}, "status": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskStatus"}, "assignedToUser": {"$ref": "#/components/schemas/StarEIP.Models.Auth.ApplicationUser"}, "taskTemplate": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskTemplate"}, "child": {"$ref": "#/components/schemas/StarEIP.Models.Child"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItemLink"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Tasks.TaskItemEmail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "taskItemId": {"type": "integer", "format": "int32"}, "taskItem": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItem"}, "emailMessageId": {"type": "integer", "format": "int32"}, "emailMessage": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.EmailMessage"}, "linkType": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.EmailLinkType"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "StarEIP.Models.Tasks.TaskItemLink": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "taskItemId": {"type": "integer", "format": "int32"}, "taskItem": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItem"}, "linkTable": {"type": "string", "nullable": true}, "linkId": {"type": "integer", "format": "int32"}, "linkType": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "StarEIP.Models.Tasks.TaskStatus": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "taskItems": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItem"}, "nullable": true}}, "additionalProperties": false}, "StarEIP.Models.Tasks.TaskTemplate": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "templateKey": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "subCategory": {"type": "string", "nullable": true}, "taskItems": {"type": "array", "items": {"$ref": "#/components/schemas/StarEIP.Models.Tasks.TaskItem"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bearer Scheme. \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}