import { sortBy } from "es-toolkit";

export default (inputSchema) => {
  const schemaNames = Object.keys(inputSchema.components.schemas);

  const schemaNameUpdatesMap = new Map(
    schemaNames.map((schemaName) => {
      let name = schemaName;

      // Handle C# generic types (e.g., "MyClass`1[[System.String, ...]]" -> "MyClassString")
      const regex = /`1\[\[System\.(\w+),/;
      const match = name.match(regex);
      if (match) {
        name = `${name.split("`")[0]}${match[1]}`;
      }

      // Handle C# nested classes (e.g., "Namespace.Outer+Inner" -> "Namespace.Outer.Inner")
      if (name.includes("+")) {
        name = name.split("+").join(".");
      }

      const className = name.split(".").slice(-1)[0];
      return [schemaName, `${className}.Schema`];
    }),
  );

  let inputSchemaString = JSON.stringify(inputSchema);

  const entriesToUpdate = Array.from(schemaNameUpdatesMap.entries());
  const sortedEntriesToUpdate = sortBy(
    entriesToUpdate,
    ([key]) => key,
  ).reverse();

  for (const [key, value] of sortedEntriesToUpdate) {
    const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    inputSchemaString = inputSchemaString.replace(
      new RegExp(escapedKey, "g"),
      value,
    );
  }

  const updatedSchema = JSON.parse(inputSchemaString);

  // 🛠 Group Endpoints by Controller Name (Tag)
  const groupedEndpoints = {};
  for (const [path, methods] of Object.entries(updatedSchema.paths)) {
    for (const [method, details] of Object.entries(methods)) {
      const tag = details.tags?.[0] || "default";

      if (!groupedEndpoints[tag]) {
        groupedEndpoints[tag] = {};
      }

      if (!groupedEndpoints[tag][method]) {
        groupedEndpoints[tag][method] = [];
      }

      groupedEndpoints[tag][method].push(path);
    }
  }

  updatedSchema["x-groupedEndpoints"] = groupedEndpoints;

  return updatedSchema;
};
