'use client';

import React, { useEffect, useState, useRef } from 'react';
import DataGrid, {
  Column,
  Editing,
  Paging,
  Pager,
  SearchPanel,
  Toolbar,
  Item,
  Lookup,
  Form
} from 'devextreme-react/data-grid';
import { createStore, CustomStore } from 'devextreme-aspnet-data-nojquery';
import Button from 'devextreme-react/button';
import urlHelpers from '@/app/urlHelpers';
import { showNotification } from '@/app/Utils/notificationUtils';
import { SimpleItem, GroupItem } from 'devextreme-react/form';
import "devextreme-react/text-area";

const categories = [
  { value: 'General', name: 'General' },
  { value: 'Patient', name: 'Patient' },
  { value: 'Authorization', name: 'Authorization' },
  { value: 'Billing', name: 'Billing' }
];

const TaskTemplateSetupPage: React.FC = () => {
  const [dataSource, setDataSource] = useState<CustomStore | null>(null);
  const dataGridRef = useRef<any>(null);

  const loadDataSource = () => {
    const serviceUrl = urlHelpers.getAbsoluteURL("api/TaskItemSetup/tasktemplates");

    setDataSource(createStore({
      key: "id",
      loadUrl: serviceUrl,
      insertUrl: serviceUrl,
      updateUrl: serviceUrl,
      deleteUrl: serviceUrl,
      onBeforeSend: (_, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem('jwtToken')}`,
        };
      },
      errorHandler(e) {
        console.error('Error with task template data:', e);
        showNotification('error', 'An error occurred while processing your request');
      },
    }));
  };

  useEffect(() => {
    loadDataSource();
  }, []);

  return (
    <DataGrid
      ref={dataGridRef}
      remoteOperations
      dataSource={dataSource}
      height="100%"
      width="100%"
      allowColumnReordering={true}
      allowColumnResizing={true}
      columnAutoWidth={true}
      showBorders={true}
      columnResizingMode={"widget"}
      showColumnLines
      twoWayBindingEnabled
      rowAlternationEnabled
      focusedRowEnabled
      autoNavigateToFocusedRow
    >
      <Toolbar>
        <Item name="searchPanel" />
        <Item location="before">
          <Button icon="refresh" onClick={loadDataSource} />
        </Item>
        <Item name="addRowButton" />
      </Toolbar>
      <SearchPanel visible={true} width={240} placeholder="Search..." />
      <Editing
        mode="popup"
        allowAdding={true}
        allowUpdating={true}
        allowDeleting={true}
        useIcons={true}
      >
        <Form>
          <GroupItem >
            <SimpleItem dataField="name" />
            <SimpleItem dataField="templateKey" />
            <SimpleItem dataField="category" editorType="dxSelectBox" />
            <SimpleItem dataField="subCategory" />
            <SimpleItem dataField="description" editorType="dxTextArea" colSpan={2} editorOptions={{ height: 100 }} />
          </GroupItem>
        </Form>
      </Editing>

      <Column dataField="id" caption="ID" allowEditing={false} />
      <Column
        dataField="name"
        caption="Template Name"
        validationRules={[{ type: 'required' }]}
      />
      <Column
        dataField="templateKey"
        caption="Template Key"
        validationRules={[{ type: 'required' }]}
      />
      <Column
        dataField="category"
        caption="Category"
        validationRules={[{ type: 'required' }]}
      >
        <Lookup
          dataSource={categories}
          valueExpr="value"
          displayExpr="name"
        />
      </Column>
      <Column
        dataField="subCategory"
        caption="Sub-Category"
      />
      <Column
        dataField="description"
        caption="Description"
        cellTemplate="descriptionTemplate"
      />

      <Paging defaultPageSize={20} />
      <Pager
        showPageSizeSelector={true}
        allowedPageSizes={[10, 20, 50]}
        showInfo={true}
      />
    </DataGrid>
  );
};

export default TaskTemplateSetupPage;
