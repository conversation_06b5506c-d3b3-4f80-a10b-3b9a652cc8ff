import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import axios from "axios";
import {
  ActionIcon,
  Group,
  Tooltip,
  FileInput,
  Progress,
  Overlay,
  Center,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import {
  IconCheck,
  IconDownload,
  IconTrash,
  IconUpload,
} from "@tabler/icons-react";
import Splitter, { Item as SplitItem } from "devextreme-react/splitter";
import {
  Column,
  Toolbar,
  Item,
  DataGrid,
  MasterDetail,
  Button as DxGridButton,
  RemoteOperations,
  ColumnFixing,
  SearchPanel,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  Paging,
  Pager,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  LoadPanel,
} from "devextreme-react/data-grid";
import dataGridExport from "@/app/Utils/dataGridExport";
import { createStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { Attachment, AttachmentsProps } from "../../../../../types/Attachment";
import ShareModal from "@/app/(admin)/admin/attachments/ShareModal";
import ShareButton from "@/app/(admin)/admin/attachments/ShareButton";

const serviceUrl = urlHelpers.getAbsoluteURL("api/Attachment");

const Attachments: React.FC<AttachmentsProps> = ({
  attachments,
  entityType,
  entityId,
  customToolbarItems,
  previewConfig,
  fetchAttachments,
}) => {
  const [selectedAttachment, setSelectedAttachment] =
    useState<Attachment | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [shareMethod, setShareMethod] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string>("");

  const handleFileUpload = async (file: File | null) => {
    if (!file) {
      notifications.show({ message: "No file selected.", color: "red" });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await axios.post(
        `${serviceUrl}/${entityId}/upload-attachment`,
        formData,
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              );
              setUploadProgress(percentCompleted);
            }
          },
        },
      );

      notifications.show({
        title: "Success",
        message: "File uploaded successfully.",
        color: "green",
        icon: <IconCheck size={18} />,
      });
      await fetchAttachments();
      setFile(null);
    } catch (e) {
      notifications.show({
        title: "Error",
        message: "Failed to upload file. Please try again later.",
        color: "red",
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleShare = async (contactId: string, method: string) => {
    if (!selectedAttachment) return;

    setIsSharing(true);

    try {
      const response = await axios.post(
        `${serviceUrl}/${entityId}/attachments/${selectedAttachment?.id}/share`,
        { contactId, sharedMethod: method },
      );

      notifications.show({
        title: "Success",
        message: "File shared successfully with the contact.",
        icon: <IconCheck size={18} />,
        color: "green",
      });
      setTimeout(() => {
        setIsShareModalOpen(false);
      }, 1500);
    } catch (error) {
      notifications.show({
        title: "Error",
        message:
          "Failed to share file with the contact. Please try again later.",
        color: "red",
      });
    } finally {
      setIsSharing(false);
    }
  };

  const handleDelete = async (attachmentId: number) => {
    try {
      const response = await axios.delete(
        `${serviceUrl}/${entityId}/attachments/${attachmentId}`,
      );
      notifications.show({
        title: "Success",
        message: "Attachment deleted successfully.",
        color: "green",
        icon: <IconCheck size={18} />,
      });
      fetchAttachments();
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Failed to delete attachment. Please try again later.",
        color: "red",
      });
    }
  };

  const handlePreview = useCallback(
    (attachment: Attachment) => {
      if (!attachment) {
        setPdfUrl("");
        return;
      }
      setTimeout(async () => {
        setSelectedAttachment(attachment);
        try {
          const fileUrl = `${serviceUrl}/${entityType}/${entityId}/${attachment.fileName}`;
          const response = await axios.get(fileUrl, { responseType: "blob" });
          const url = URL.createObjectURL(response.data);
          setPdfUrl(url);
        } catch (error) {
          console.error("Error fetching PDF:", error);
        }
      }, 100);
    },
    [previewConfig],
  );

  const handleFocusedRowChanging = (e: any) => {
    const attachment = e.row && e.row.data;
    handlePreview(attachment);
  };

  const renderShareLogs = (options: any) => {
    if (!options && !options.data) return null;

    const attachment = options.data.data;

    const shareLogs = createStore({
      key: "id",
      loadUrl: `${serviceUrl}/${entityId}/attachments/${attachment.id}/share-logs`,
      onBeforeSend: (e, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        };
      },
      errorHandler: (e) => {
        console.error("Error occurred:", e);
        notifications.show({
          title: "Error",
          message: "Failed to fetch share logs. Please try again later.",
          color: "red",
        });
      },
    });

    return (
      <DataGrid
        dataSource={shareLogs}
        showBorders={true}
        columnAutoWidth={true}
        remoteOperations={true}
        height={200}
      >
        <Column
          dataField="sharedOn"
          caption="Shared On"
          dataType="datetime"
          format="yyyy-MM-dd HH:mm:ss"
        />
        <Column dataField="contactName" caption="Shared By" dataType="string" />
        <Column
          dataField="sharedMethod"
          caption="Shared Method"
          dataType="string"
        />
        <LoadPanel enabled={true} />
      </DataGrid>
    );
  };

  const renderActions = (cellData: { data: Attachment }) => {
    const currentAttachment = cellData.data;

    return (
      <Group gap={8} onClick={(e: any) => e.stopPropagation()}>
        <Tooltip label="Share">
          <ShareButton
            attachment={currentAttachment}
            onShare={(method: string) => {
              console.log("SelectedAttachment:", currentAttachment);
              setSelectedAttachment(currentAttachment);
              setShareMethod(method);
              setIsShareModalOpen(true);
            }}
          />
        </Tooltip>

        <Tooltip label="Download">
          <ActionIcon
            onClick={(e: any) => {
              e.stopPropagation();
            }}
          >
            <IconDownload size={18} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon
            onClick={(e: any) => {
              e.stopPropagation();
              handleDelete(cellData.data.id);
            }}
          >
            <IconTrash size={18} />
          </ActionIcon>
        </Tooltip>
      </Group>
    );
  };

  return (
    <>
      <Splitter orientation="horizontal" height="100%">
        <SplitItem>
          <DataGrid
            dataSource={attachments}
            keyExpr="id"
            onExporting={dataGridExport}
            height="100%"
            width="100%"
            allowColumnReordering={true}
            allowColumnResizing={true}
            columnAutoWidth={true}
            showBorders={true}
            columnResizingMode={"widget"}
            showColumnLines={false}
            twoWayBindingEnabled
            rowAlternationEnabled
            focusedRowEnabled
            autoNavigateToFocusedRow
            loadPanel={{ enabled: true }}
            hoverStateEnabled={true}
            onFocusedRowChanged={handleFocusedRowChanging}
          >
            <Toolbar>
              <Item location="before">
                <Group gap={8}>
                  <FileInput
                    placeholder="Upload File"
                    color="primary"
                    accept="application/pdf"
                    leftSection={<IconUpload size={18} />}
                    value={file}
                    onChange={async (file) => {
                      setFile(file);
                      await handleFileUpload(file);
                    }}
                    disabled={isLoading || isUploading}
                  />
                  {customToolbarItems}
                </Group>
              </Item>
              <Item location="before">
                <DxGridButton icon="refresh" onClick={fetchAttachments} />
              </Item>
              <Item name="searchPanel" locateInMenu="auto" location="before" />
              <Item name="groupPanel" locateInMenu="auto" location="before" />
              <Item name="exportButton" locateInMenu="auto" location="after" />
              <Item
                name="applyFilterButton"
                locateInMenu="auto"
                location="after"
              />
              <Item name="revertButton" locateInMenu="auto" location="after" />
              <Item name="saveButton" locateInMenu="auto" location="after" />
              <Item
                name="columnChooserButton"
                locateInMenu="auto"
                location="after"
              />
            </Toolbar>

            <RemoteOperations groupPaging={true} />
            <ColumnFixing enabled={true} />
            <SearchPanel visible width={250} />
            <HeaderFilter visible />
            <ColumnChooser enabled />
            <Sorting mode="multiple" />
            <ColumnFixing />
            <Paging defaultPageSize={40} />
            <Pager showPageSizeSelector />
            <FilterPanel visible />
            <FilterBuilderPopup />
            <Export enabled={true} />

            <Column dataField="id" visible={false} />
            <Column dataField="fileName" caption="File Name" />
            <Column
              dataField="createdAt"
              caption="Uploaded"
              dataType="date"
              format="shortDateShortTime"
            />
            <Column dataField="createdBy" />
            <Column
              dataField="actions"
              cellRender={renderActions}
              allowFiltering={false}
              caption=""
              width={115}
              fixed
              fixedPosition="right"
            />

            <MasterDetail enabled={true} component={renderShareLogs} />
          </DataGrid>
        </SplitItem>
        <SplitItem>
          <object
            style={{ height: "calc(100% - 4px)", width: "100%", flex: 1 }}
            id="myPdfObject"
            data={pdfUrl}
            type="application/pdf"
          >
            <p>
              Alternative text - include a link <a href={pdfUrl}>to the PDF!</a>
            </p>
          </object>
        </SplitItem>
      </Splitter>

      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => {
          setIsShareModalOpen(false);
          setShareMethod(null);
        }}
        onShare={handleShare}
        isSharing={isSharing}
        shareMethod={shareMethod!}
      />

      {isUploading && (
        <Overlay color="#000" center>
          <Center>
            <Progress
              value={uploadProgress}
              size="xl"
              radius="xl"
              transitionDuration={200}
              w={300}
            />
          </Center>
        </Overlay>
      )}
    </>
  );
};

export default Attachments;
