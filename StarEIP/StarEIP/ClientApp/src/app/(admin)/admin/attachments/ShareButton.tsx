import { ActionIcon, Indicator, Menu } from "@mantine/core";
import { Attachment } from "../../../../../types/Attachment";
import {
  IconMail,
  IconMailbox,
  IconPrinter,
  IconShare,
} from "@tabler/icons-react";
import React from "react";

interface ShareButtonProps {
  attachment: Attachment;
  onShare: (method: string) => void;
}

const ShareButton = ({ attachment, onShare }: ShareButtonProps) => {
  return (
    <Menu position="bottom-end" withinPortal>
      <Menu.Target>
        <div>
          <Indicator
            inline
            label={attachment.shareCount}
            color="purple"
            disabled={attachment.shareCount === 0}
            size={16}
            position="top-end"
            offset={4}
          >
            <ActionIcon>
              <IconShare size={18} />
            </ActionIcon>
          </Indicator>
        </div>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Item
          leftSection={<IconPrinter size={14} />}
          onClick={(e: any) => {
            e.stopPropagation();
            onShare("Fax");
          }}
        >
          Fax
        </Menu.Item>
        <Menu.Item
          leftSection={<IconMail size={14} />}
          disabled
          onClick={(e: any) => {
            e.stopPropagation();
            onShare("Email");
          }}
        >
          Email
        </Menu.Item>
        <Menu.Item
          leftSection={<IconMailbox size={14} />}
          disabled
          onClick={(e: any) => {
            e.stopPropagation();
            onShare("Mail");
          }}
        >
          Mail
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default ShareButton;
