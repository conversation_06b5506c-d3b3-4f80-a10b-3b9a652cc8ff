"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  Column,
  Pager,
  Paging,
  FilterRow,
  HeaderFilter,
  SearchPanel,
  Toolbar,
  Item,
  Export,
  Selection,
} from "devextreme-react/data-grid";
import { useRouter } from "next/navigation";
import urlHelpers from "@/app/urlHelpers";
import { Button as DxButton } from "devextreme-react/button";
import { formatDate } from "@/app/Utils/dateUtils";
import dataGridExport from "@/app/Utils/dataGridExport";
import { Toolt<PERSON>, Drawer } from "@mantine/core";
import { IconSearch } from "@tabler/icons-react";
import ChildMappingDrawer from "../patients/components/ChildMappingDrawer";

const serviceUrl = urlHelpers.getAbsoluteURL("api/import-child-detail-report");

interface ChildDetailReport {
  id: number;
  eiChildId: string;
  childFirstName: string;
  childLastName: string;
  dob?: Date;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  gender?: string;
  primaryLanguage?: string;
  referralReason?: string;
  referralMethod?: string;
  guardianFirstName?: string;
  guardianLastName?: string;
  guardianPhone?: string;
}

const ChildDetailReportsTable: React.FC = () => {
  const [dataSource, setDataSource] = useState<any>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRowData, setSelectedRowData] =
    useState<ChildDetailReport | null>(null);
  const [drawerOpened, setDrawerOpened] = useState(false);
  const router = useRouter();
  const dataGridRef = useRef(null);

  // Helper function to safely convert string dates to Date objects
  const parseDate = (dateValue: any): Date | undefined => {
    if (!dateValue) return undefined;
    if (dateValue instanceof Date) return dateValue;
    if (typeof dateValue === "string") {
      const parsed = new Date(dateValue);
      return isNaN(parsed.getTime()) ? undefined : parsed;
    }
    return undefined;
  };

  const loadDataSource = useCallback(() => {
    const ds = createStore({
      key: "id",
      loadUrl: serviceUrl,
      onBeforeSend: (_, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        };
      },
      errorHandler: (e) => {
        if (e.message === "Unauthorized") {
          router.push("/login");
        }
      },
    });
    setDataSource(ds);
  }, [router]);

  useEffect(() => {
    loadDataSource();
  }, [loadDataSource]);

  const handleSelectionChanged = (e: any) => {
    setSelectedRowKeys(e.selectedRowKeys);
    if (e.selectedRowsData && e.selectedRowsData.length > 0) {
      setSelectedRowData(e.selectedRowsData[0]);
    } else {
      setSelectedRowData(null);
    }
  };

  const renderEiChildIdCell = (data: { data: ChildDetailReport }) => {
    const row = data.data;
    return (
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <span>{row.eiChildId}</span>
        <div onClick={(e) => e.stopPropagation()}>
          <Tooltip label="Map to a patient" position="right" withArrow>
            <div>
              <ChildMappingDrawer
                eiNumber={row.eiChildId}
                onMappingSuccess={loadDataSource}
                buttonText={<IconSearch size="1rem" />}
                buttonSize="sm"
                childData={{
                  firstName: row.childFirstName,
                  lastName: row.childLastName,
                  dateOfBirth: parseDate(row.dob)?.toISOString(),
                  fullAddress: row.address + (row.city ? ", " + row.city : "") + (row.state ? ", " + row.state : "") + (row.zip ? " " + row.zip : ""),
                  gender: row.gender,
                  primaryLanguage: row.primaryLanguage,
                  reasonForReferral: row.referralReason,
                  referralMethod: row.referralMethod,
                  parentName: row.guardianFirstName + " " + row.guardianLastName,
                  parentPhoneNumber: row.guardianPhone,
                }}
              />
            </div>
          </Tooltip>
        </div>
      </div>
    );
  };

  const formatDateTime = (cellData: any) => {
    return cellData.value ? formatDate(new Date(cellData.value)) : "";
  };

  return (
    <>
      <DataGrid
        ref={dataGridRef}
        dataSource={dataSource}
        showBorders={true}
        remoteOperations={true}
        allowColumnReordering={true}
        rowAlternationEnabled={true}
        height="100%"
        width="100%"
        allowColumnResizing={true}
        columnAutoWidth={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        onExporting={dataGridExport}
        onSelectionChanged={handleSelectionChanged}
      >
        <Selection mode="single" />
        <FilterRow visible={true} />
        <HeaderFilter visible={true} />
        <SearchPanel visible={true} width={240} placeholder="Search..." />

        <Column dataField="id" caption="ID" width={80} visible={false} />
        <Column
          dataField="eiChildId"
          caption="EI Child ID"
          cellRender={renderEiChildIdCell}
        />
        <Column dataField="childFirstName" caption="First Name" />
        <Column dataField="childLastName" caption="Last Name" />
        <Column
          dataField="dob"
          caption="Date of Birth"
          dataType="date"
          cellRender={formatDateTime}
        />
        <Column dataField="address" caption="Address" />
        <Column dataField="city" caption="City" />
        <Column dataField="state" caption="State" />
        <Column dataField="zip" caption="Zip" />
        <Column dataField="gender" caption="Gender" />
        <Column dataField="ethnicity" caption="Ethnicity" />
        <Column dataField="race" caption="Race" />
        <Column dataField="childAge" caption="Child Age" />
        <Column dataField="primaryLanguage" caption="Primary Language" />
        <Column dataField="countyOfResidence" caption="County of Residence" />
        <Column dataField="addressCounty" caption="Address County" />
        <Column
          dataField="referralDate"
          caption="Referral Date"
          dataType="date"
          cellRender={formatDateTime}
        />
        <Column
          dataField="atRiskReferralDate"
          caption="At Risk Referral Date"
          dataType="date"
          cellRender={formatDateTime}
        />
        <Column
          dataField="referralAtRiskStatus"
          caption="Referral At Risk Status"
        />
        <Column dataField="referralType" caption="Referral Type" />
        <Column dataField="referralMethod" caption="Referral Method" />
        <Column dataField="referralReason" caption="Referral Reason" />
        <Column dataField="referralSourceType" caption="Referral Source Type" />
        <Column dataField="eligibilityStatus" caption="Eligibility Status" />
        <Column dataField="guardianLastName" caption="Guardian Last Name" />
        <Column dataField="guardianFirstName" caption="Guardian First Name" />
        <Column dataField="guardianPhone" caption="Guardian Phone" />
        <Column
          dataField="coordinatorLastName"
          caption="Coordinator Last Name"
        />
        <Column
          dataField="coordinatorFirstName"
          caption="Coordinator First Name"
        />
        <Column dataField="coordinatorPhone" caption="Coordinator Phone" />
        <Column dataField="coordinatorCompany" caption="Coordinator Company" />
        <Column dataField="eiodLastName" caption="EIOD Last Name" />
        <Column dataField="eiodFirstName" caption="EIOD First Name" />
        <Column
          dataField="currentIfspStartDate"
          caption="Current IFSP Start Date"
          dataType="date"
          cellRender={formatDateTime}
        />
        <Column
          dataField="currentIfspEndDate"
          caption="Current IFSP End Date"
          dataType="date"
          cellRender={formatDateTime}
        />
        <Column dataField="currentIfspStatus" caption="Current IFSP Status" />
        <Column dataField="currentIfspType" caption="Current IFSP Type" />
        <Column
          dataField="createdAt"
          caption="Created At"
          dataType="date"
          format="shortDateShortTime"
        />
        <Column
          dataField="updatedAt"
          caption="Updated At"
          dataType="date"
          format="shortDateShortTime"
        />
        <Column
          dataField="lastImportedAt"
          caption="Last Imported At"
          dataType="date"
          format="shortDateShortTime"
        />

        <Paging defaultPageSize={20} />
        <Pager
          showPageSizeSelector={true}
          allowedPageSizes={[10, 20, 50, 100]}
        />

        <Export enabled={true} allowExportSelectedData={true} />

        <Toolbar>
          <Item location="before">
            <DxButton icon="refresh" onClick={loadDataSource} />
          </Item>
          <Item location="before">
            <DxButton
              text="Map Selected"
              icon="map"
              disabled={!selectedRowData}
              onClick={() => {
                if (selectedRowData) {
                  setDrawerOpened(true);
                }
              }}
            />
          </Item>
          <Item name="searchPanel" />
          <Item name="exportButton" />
        </Toolbar>
      </DataGrid>

      {selectedRowData && (
        <Drawer
          opened={drawerOpened}
          onClose={() => setDrawerOpened(false)}
          position="right"
          size="50%"
          padding="xs"
          styles={{
            content: { display: "flex", flexDirection: "column" },
            body: { flex: 1, overflow: "auto" },
          }}
          overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
          closeOnClickOutside
          title={`Map EI Number: ${selectedRowData.eiChildId}`}
        >
          <ChildMappingDrawer
            eiNumber={selectedRowData.eiChildId}
            onMappingSuccess={() => {
              loadDataSource();
              setDrawerOpened(false);
            }}
            buttonText="Map to Child"
            buttonSize="md"
            childData={{
              firstName: selectedRowData.childFirstName,
              lastName: selectedRowData.childLastName,
              dateOfBirth: parseDate(selectedRowData.dob)?.toISOString(),
              fullAddress: selectedRowData.address + (selectedRowData.city ? ", " + selectedRowData.city : "") + (selectedRowData.state ? ", " + selectedRowData.state : "") + (selectedRowData.zip ? " " + selectedRowData.zip : ""),
              gender: selectedRowData.gender,
              primaryLanguage: selectedRowData.primaryLanguage,
              reasonForReferral: selectedRowData.referralReason,
              referralMethod: selectedRowData.referralMethod,
              parentName: selectedRowData.guardianFirstName + " " + selectedRowData.guardianLastName,
              parentPhoneNumber: selectedRowData.guardianPhone,
            }}
          />
        </Drawer>
      )}
    </>
  );
};

export default ChildDetailReportsTable;
