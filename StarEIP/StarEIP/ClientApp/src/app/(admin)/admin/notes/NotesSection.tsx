import React, { useEffect, useState } from "react";
import { Note } from "../../../../../types/Child";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { notifications } from "@mantine/notifications";
import {
  ActionIcon,
  Avatar,
  Button,
  Divider,
  Group,
  Stack,
  Text,
  Textarea,
  Tooltip,
} from "@mantine/core";
import { IconClock, IconSend, IconTrash } from "@tabler/icons-react";

interface NotesProps {
  entityType: "child" | "physician" | "task" | "authorization";
  entityId: number;
  onNoteAdded?: () => void;
  onNoteDeleted?: () => void;
  enableDelete?: boolean;
  maxLength?: number;
}

const serviceUrl = urlHelpers.getAbsoluteURL("api/notes");

const getInitials = (name: string) => {
  if (!name) return "";

  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

const getRandomColor = (name: string) => {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = hash % 360;
  return `hsl(${hue}, 70%, 60%)`;
};

const formatTimeAgo = (date: string) => {
  const now = new Date();
  const noteDate = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - noteDate.getTime()) / 1000);

  if (diffInSeconds < 60) return "just now";
  if (diffInSeconds < 3600)
    return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400)
    return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 604800)
    return `${Math.floor(diffInSeconds / 86400)} days ago`;

  return noteDate.toLocaleString();
};

export const NotesSection: React.FC<NotesProps> = ({
  entityType,
  entityId,
  onNoteAdded,
  onNoteDeleted,
  enableDelete = true,
  maxLength = 500,
}) => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [newNote, setNewNote] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchNotes = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(
        `${serviceUrl}/${entityType}/${entityId}`,
      );
      setNotes(response.data);
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Error fetching notes",
        color: "red",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (entityId) {
      fetchNotes();
    }
  }, [entityId, entityType]);

  const handleAddNote = async () => {
    if (!newNote.trim() || isSubmitting) return;
    setIsSubmitting(true);

    try {
      const response = await axios.post(
        `${serviceUrl}/${entityType}/${entityId}`,
        {
          note: newNote,
        },
      );

      await fetchNotes();

      setNewNote("");
      onNoteAdded?.();

      notifications.show({
        title: "Success",
        message: "Note added successfully",
        color: "green",
      });
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Error adding note",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteNote = async (noteId: number) => {
    try {
      await axios.delete(`${serviceUrl}/${noteId}`);

      setNotes((prev) => prev.filter((note) => note.id !== noteId));
      onNoteDeleted?.();

      notifications.show({
        title: "Success",
        message: "Note deleted successfully",
        color: "green",
      });
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Error deleting note",
        color: "red",
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.ctrlKey) {
      handleAddNote();
    }
  };

  return (
    <Stack gap="xs" style={{ flex: 1 }} m="sm">
      <Stack gap="xs" style={{ height: "100%", overflow: "auto", flex: 1 }}>
        {isLoading ? (
          <Text color="dimmed" size="sm" ta="center">
            Loading notes...
          </Text>
        ) : notes.length === 0 ? (
          <Text color="dimmed" size="sm" ta="center">
            No notes available
          </Text>
        ) : (
          notes.map((note) => (
            <Group
              className="parent-hover-tracker"
              key={note.id}
              gap="sm"
              align="flex-start"
            >
              <Avatar
                size="sm"
                color={getRandomColor(note.createdBy)}
                radius="xl"
              >
                {getInitials(note.createdBy)}
              </Avatar>

              <Stack
                gap="2"
                style={{
                  flex: 1,
                  color: "var(--mantine-color-text)",
                  lineHeight: "1.5",
                }}
              >
                <Text size="sm" style={{ whiteSpace: "pre-wrap" }}>
                  {note.note}
                </Text>

                <Group gap={0} justify="space-between">
                  <Group gap="xs" style={{ alignItems: "center" }}>
                    <IconClock
                      size={10}
                      style={{ color: "var(--mantine-color-gray-5)" }}
                    />
                    <Tooltip label={new Date(note.createdOn).toLocaleString()}>
                      <Text size="xs" c="dimmed" span>
                        {formatTimeAgo(note.createdOn)}
                      </Text>
                    </Tooltip>
                    <Text size="xs" c="dimmed" span>
                      {note.createdBy}
                    </Text>
                  </Group>
                  {enableDelete && (
                    <ActionIcon
                      size="sm"
                      color="red"
                      variant="subtle"
                      onClick={() => handleDeleteNote(note.id)}
                      style={{ marginLeft: "auto" }}
                      className="hidden-child"
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  )}
                </Group>
              </Stack>
            </Group>
          ))
        )}
      </Stack>

      <Divider my="xs" labelPosition="center" />

      <Textarea
        placeholder="Add a note... "
        value={newNote}
        onChange={(e) => setNewNote(e.currentTarget.value)}
        minRows={3}
        maxRows={6}
        autosize
        maxLength={maxLength}
        disabled={isSubmitting}
        style={{
          input: {
            fontFamily: "Roboto",
            fontSize: "14px",
            lineHeight: "1.5",
          },
        }}
        onKeyDown={handleKeyPress}
      />

      <Group justify="space-between" align="center" mt="xs">
        <Text size="xs" color="dimmed">
          {newNote.length}/{maxLength} characters
        </Text>
        <Button
          size="sm"
          rightSection={<IconSend size={16} />}
          onClick={handleAddNote}
          loading={isSubmitting}
          disabled={!newNote.trim() || isSubmitting}
        >
          Add Note
        </Button>
      </Group>
    </Stack>
  );
};
