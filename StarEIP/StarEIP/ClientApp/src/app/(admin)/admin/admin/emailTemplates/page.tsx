"use client";

import React, { useEffect, useState, useMemo } from "react";
import Splitter, { Item as SplitterItem } from "devextreme-react/splitter";
import HtmlEditor, {
  Toolbar as HtmlToolbar,
  MediaResizing,
  ImageUpload,
  Item as HtmlItem,
} from "devextreme-react/html-editor";
import CustomStore from "devextreme/data/custom_store";
import { useRouter } from "next/navigation";
import urlHelpers from "@/app/urlHelpers";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Editing,
  Toolbar,
  Item,
  Popup,
  Form,
  Button as DgButton,
} from "devextreme-react/data-grid";
import { Item as FormItem } from "devextreme-react/form";
import {
  SimpleItem,
  GroupItem,
  TabbedItem,
  TabPanelOptions,
  Tab,
} from "devextreme-react/form";
import "devextreme-react/text-area";

import Button from "devextreme-react/button";

const serviceUrl = urlHelpers.getAbsoluteURL("api/emailTemplates");
interface EmailTemplate {
  name: string;
  subject: string;
  body: string;
}

const EmailTemplatesPage: React.FC = () => {
  const [dataSource, setRemoteDataSource] = useState<CustomStore>();
  const [showHtmlEditor, setShowHtmlEditor] = useState(true);
  const [selectedEmailTemplate, setSelectedEmailTemplate] =
    useState<EmailTemplate | null>(null);

  const router = useRouter();

  const loadChildDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "name",
        loadUrl: serviceUrl,
        insertUrl: `${serviceUrl}/create`,
        updateUrl: `${serviceUrl}/update`,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  };

  useEffect(() => {
    loadChildDataSource();
  }, []);

  const handleFocusedRowChanging = (e: any) => {
    setSelectedEmailTemplate(e.row && e.row.data);
  };

  const CheckBoxOptions = {
    text: "Show Html Editor",
    value: showHtmlEditor,
    onValueChanged: (e: any) => {
      setShowHtmlEditor(e.component.option("value"));
    },
  };

  return (
    <Splitter>
      <SplitterItem>
        <DataGrid
          remoteOperations
          dataSource={dataSource}
          height="100%"
          keyExpr="name"
          allowColumnReordering={true}
          allowColumnResizing={true}
          columnAutoWidth={true}
          showBorders={true}
          columnResizingMode={"widget"}
          showColumnLines={false}
          twoWayBindingEnabled
          rowAlternationEnabled
          focusedRowEnabled
          autoNavigateToFocusedRow
          onFocusedRowChanged={handleFocusedRowChanging}
        >
          <Toolbar>
            <Item location="before">
              <Button icon="refresh" onClick={loadChildDataSource} />
            </Item>
            <Item name="searchPanel" locateInMenu="auto" location="before" />
            <Item name="addRowButton" location="before" />
            {/* <Item location="before">
                            <Button icon="add" text='Add User' onClick={open} />
                        </Item> */}
            <Item name="groupPanel" locateInMenu="auto" location="before" />
            <Item name="exportButton" locateInMenu="auto" location="after" />
            <Item
              name="applyFilterButton"
              locateInMenu="auto"
              location="after"
            />
            <Item name="revertButton" locateInMenu="auto" location="after" />
            <Item name="saveButton" locateInMenu="auto" location="after" />
            <Item
              name="columnChooserButton"
              locateInMenu="auto"
              location="after"
            />
          </Toolbar>
          <RemoteOperations groupPaging={true} />
          <ColumnFixing enabled={true} />
          <SearchPanel visible width={250} />
          <HeaderFilter visible />
          <ColumnChooser enabled />
          <Sorting mode="multiple" />
          <ColumnFixing />
          <Paging defaultPageSize={40} />
          <Pager showPageSizeSelector />
          <FilterPanel visible />
          <FilterBuilderPopup />
          <Editing mode="popup" allowUpdating={true} allowAdding={true}>
            <Popup
              title="Email Template Info"
              showTitle={true}
              width="1000"
              height={800}
            />
            <Form>
              <FormItem dataField="name" colSpan={2} />
              <FormItem
                dataField="subject"
                colSpan={2}
                editorType="dxTextArea"
                editorOptions={{ height: 75 }}
              />
              <SimpleItem
                editorType="dxCheckBox"
                editorOptions={CheckBoxOptions}
                colSpan={2}
              />
              {showHtmlEditor && (
                <FormItem
                  dataField="body"
                  editorType="dxHtmlEditor"
                  colSpan={2}
                  editorOptions={{
                    height: "385",
                    with: "100%",
                    toolbar: {
                      items: [
                        "undo",
                        "redo",
                        "separator",
                        "size",
                        "font",
                        "separator",
                        "bold",
                        "italic",
                        "strike",
                        "underline",
                        "separator",
                        "alignLeft",
                        "alignCenter",
                        "alignRight",
                        "alignJustify",
                        "separator",
                        "orderedList",
                        "bulletList",
                        "separator",
                        "header",
                        "separator",
                        "color",
                        "background",
                        "separator",
                        "link",
                        "image",
                        "separator",
                        "clear",
                        "codeBlock",
                        "blockquote",
                        "separator",
                        "insertTable",
                        "deleteTable",
                        "insertRowAbove",
                        "insertRowBelow",
                        "deleteRow",
                        "insertColumnLeft",
                        "insertColumnRight",
                        "deleteColumn",
                      ],
                    },
                    mediaResizing: { enabled: true },
                  }}
                />
              )}
              {!showHtmlEditor && (
                <FormItem
                  dataField="body"
                  colSpan={2}
                  editorType="dxTextArea"
                  editorOptions={{ height: "375", with: "100%" }}
                />
              )}
            </Form>
          </Editing>

          <Export enabled={true} />
          <Column dataField="name" caption="Name" />
          <Column dataField="subject" caption="Subject" />
          <Column dataField="body" caption="Body" visible={false} />
        </DataGrid>
      </SplitterItem>
      <SplitterItem>
        <HtmlEditor
          value={selectedEmailTemplate?.body || ""}
          readOnly={true}
          height="100%"
        />
      </SplitterItem>
    </Splitter>
  );
};

export default EmailTemplatesPage;
