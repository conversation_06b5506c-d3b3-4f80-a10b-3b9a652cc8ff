"use client";

import React, { useC<PERSON>back, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  ActionIcon,
  Badge,
  Button,
  Card,
  Group,
  Stack,
  Text,
  Title,
  Tooltip,
} from "@mantine/core";
import urlHelpers from "@/app/urlHelpers";
import axios from "axios";
import { Popup } from "devextreme-react/popup";
import AuthorizationDetailsPopup from "../authorizations/AuthorizationDetailsPopup";
import { useDisclosure } from "@mantine/hooks";
import { Authorization } from "../../../../../types/Authorization";
import { IconPlus } from "@tabler/icons-react";
import AuthorizationCard from "@/app/(admin)/admin/authorizations/components/AuthorizationCard";

const serviceUrl = urlHelpers.getAbsoluteURL("api/authorizations");

interface CompactAuthorizationsTableProps {
  childId: number;
}

const InfoGroup: React.FC<{
  icon?: React.JSX.Element | undefined;
  label: string | undefined;
  value: string | undefined;
}> = ({ icon, label, value }) => (
  <Tooltip label={label}>
    <Group>
      {icon && icon}
      {!icon && (
        <Text
          fw={400}
          size="14px"
          style={{ fontFamily: "Roboto", lineHeight: "18px" }}
          c="dimmed"
        >
          {label}:
        </Text>
      )}
      <Text
        fw={500}
        size="14px"
        style={{
          fontFamily: "Roboto",
          lineHeight: "18px",
          color: "rgba(0, 0, 0, 0.44)",
        }}
      >
        {value}
      </Text>
    </Group>
  </Tooltip>
);

const CompactAuthorizationsTable: React.FC<CompactAuthorizationsTableProps> = ({
  childId,
}) => {
  const router = useRouter();
  const [authorizations, setAuthorizations] = useState<any[]>([]);
  const [isFormVisible, { toggle, close, open }] = useDisclosure(false);
  const [editingAuthorization, setEditingAuthorization] =
    useState<Authorization | null>(null);

  const loadAuthorizations = useCallback(async () => {
    const data = await axios.get(`${serviceUrl}/child/${childId}`);
    setAuthorizations(data.data);
  }, [childId]);

  useEffect(() => {
    loadAuthorizations();
  }, [childId, loadAuthorizations]);

  const handleAuthorizationSubmitted = useCallback(() => {
    loadAuthorizations();
    close();
    setEditingAuthorization(null);
  }, [childId, loadAuthorizations]);

  return (
    <>
      <Stack>
        <Group justify="end" mt="sm" mr="sm">
          <Button rightSection={<IconPlus size={14} />} onClick={open}>
            Add Authorization
          </Button>
        </Group>
        {authorizations.map((authorization) => (
          // <Card key={authorization.id} shadow="xs" padding="md" radius="md">
          //     <Group justify="space-between">
          //         <Text fw={500} size='14px'
          //             style={{ fontFamily: 'Roboto', lineHeight: '18px', fontSize: '14px' }}>
          //             Auth #{authorization.authNumber}
          //         </Text>
          //         <Badge>{authorization.statusId}</Badge>
          //     </Group>
          //     <InfoGroup label="SC" value={authorization.userName} />
          //     <InfoGroup label="Type" value={authorization.authType} />
          //     {authorization.startDate && authorization.endDate && <InfoGroup label="Date" value={`${new Date(authorization.startDate).toLocaleDateString('en-US')} - ${new Date(authorization.endDate).toLocaleDateString('en-US')}`} />}
          //     {authorization.units && <InfoGroup label="Unites" value={authorization.units} />}
          // </Card>
          <AuthorizationCard
            key={authorization.id}
            authorization={authorization}
            showStatus={true}
          />
        ))}
      </Stack>
      {isFormVisible && (
        <Popup
          visible={isFormVisible}
          onHiding={close}
          title={
            editingAuthorization
              ? "Edit Authorization"
              : "Add New Authorization"
          }
          showCloseButton={true}
          width={800}
          height={"auto"}
        >
          <AuthorizationDetailsPopup
            closeDetails={handleAuthorizationSubmitted}
            initialData={editingAuthorization || undefined}
            isEditMode={!!editingAuthorization}
            childId={childId} // Pass the childId here
          />
        </Popup>
      )}
    </>
  );
};

export default CompactAuthorizationsTable;
