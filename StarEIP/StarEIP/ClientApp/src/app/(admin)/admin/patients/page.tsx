"use client";
import React, { useMemo, useState } from "react";
import { Item as GridItem } from "devextreme-react/data-grid";
import Splitter, { Item } from "devextreme-react/splitter";
import PatientTable from "./PatientTable";
import { ButtonGroup, ButtonGroupTypes } from "devextreme-react/button-group";
import notify from "devextreme/ui/notify";
import ChildInfo from "./ChildInfo";
import "./styles.css";
import { Stack } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { ChildDetailsDtoSchema } from "@/api/types";

const displayOptions = [
  {
    icon: "pinright",
    id: "overlay",
    hint: "left overlay panel",
  },
  {
    icon: "panelleft",
    id: "pin",
    hint: "pin left",
  },
];

const ChildrenPage = () => {
  const [selectedChild, setSelectedChild] =
    useState<ChildDetailsDtoSchema | null>(null);
  const matches = useMediaQuery("(min-width: 56.25em)");

  const itemClick = (e: ButtonGroupTypes.ItemClickEvent) => {
    notify(
      { message: `The "${e.itemData.hint}" button was clicked`, width: 320 },
      "success",
      1000,
    );
  };

  const renderGridItem = (
    <GridItem location="after">
      <ButtonGroup
        items={displayOptions}
        keyExpr="id"
        stylingMode="outlined"
        defaultSelectedItemKeys={["pin"]}
        onItemClick={itemClick}
      />
    </GridItem>
  );

  const renderChildrenTable = useMemo(() => {
    return function RenderChildrenTable() {
      return (
        <PatientTable
          item={renderGridItem}
          showToolbar={true}
          onFocusedChildChanging={(child) => setSelectedChild(child)}
        />
      );
    };
  }, []);

  const renderDetails = () => {
    return (
      <Stack gap="md" style={{ height: "100%", overflow: "hidden" }}>
        <ChildInfo child={selectedChild} showAuthorizationsTab />
      </Stack>
    );
  };

  return (
    <Splitter id="splitter">
      <Item
        resizable={true}
        size="75%"
        minSize="70px"
        render={renderChildrenTable}
      />
      {matches && (
        <Item
          resizable={true}
          minSize="250px"
          render={renderDetails}
          collapsible
        />
      )}
    </Splitter>
  );
};

export default ChildrenPage;
