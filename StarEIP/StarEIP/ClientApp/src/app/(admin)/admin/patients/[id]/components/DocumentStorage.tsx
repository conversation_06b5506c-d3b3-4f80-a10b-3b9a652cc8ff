import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import { notifications } from "@mantine/notifications";
import urlHelpers from "@/app/urlHelpers";
import { useRouter } from "next/navigation";
import Attachments from "@/app/(admin)/admin/attachments/Attachments";
import { Button, Center, Menu, Modal, Overlay, Progress } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import ReportPreview from "../../../reports/ReportPreview";
import { Attachment } from "../../../../../../../types/Attachment";

const serviceUrl = urlHelpers.getAbsoluteURL("api/Attachment");

interface DocumentStorageProps {
  childId: number;
  authId?: number;
}

interface ReportItem {
  id: number;
  name: string;
  displayName: string;
}

const DocumentStorage = ({ childId, authId }: DocumentStorageProps) => {
  const router = useRouter();
  const [opened, { open, close }] = useDisclosure(false);
  const [availableReports, setAvailableReports] = useState<ReportItem[]>([]);
  const [selectedReport, setSelectedReport] = useState<string>("");
  const [attachments, setAttachments] = useState<Attachment[]>([]);

  useEffect(() => {
    fetchAttachments();
    fetchAvailableReports();
  }, [childId, authId]);

  const fetchAvailableReports = async () => {
    try {
      const url = authId
        ? `${urlHelpers.getAbsoluteURL("api/reports/child-details")}?isAuthDetail=true`
        : `${urlHelpers.getAbsoluteURL("api/reports/child-details")}`;

      const response = await axios.get(url);
      if (response.status === 200) {
        setAvailableReports(response.data);
      } else {
        notifications.show({
          message: "Failed to fetch reports.",
          color: "red",
        });
      }
    } catch (e) {
      notifications.show({
        title: "Error",
        message: "Failed to fetch reports. Please try again later.",
        color: "red",
      });
    }
  };

  const fetchAttachments = async () => {
    try {
      const response = await axios.get(`${serviceUrl}/${childId}/attachments`);

      const sortedAttachments = response.data.sort(
        (a: Attachment, b: Attachment) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );

      setAttachments(sortedAttachments);
    } catch (e) {
      notifications.show({
        title: "Error",
        message: "Failed to fetch attachments. Please try again later.",
        color: "red",
      });
    }
  };

  const handleReportSelect = useCallback(
    (reportName: string) => {
      setSelectedReport(reportName);
      open();
    },
    [router, childId],
  );

  const renderCustomToolbarItems = () => (
    <Menu>
      <Menu.Target>
        <Button>Create Report</Button>
      </Menu.Target>
      <Menu.Dropdown>
        {availableReports.map((report) => (
          <Menu.Item
            key={report.id}
            onClick={() => handleReportSelect(report.name)}
          >
            {report.displayName}
          </Menu.Item>
        ))}
      </Menu.Dropdown>
    </Menu>
  );

  return (
    <>
      <Attachments
        attachments={attachments}
        entityType="child-attachments"
        entityId={childId}
        customToolbarItems={renderCustomToolbarItems()}
        previewConfig={{
          containerType: "child-attachments",
          baseUrl: "/blazor/pdf",
        }}
        fetchAttachments={fetchAttachments}
      />
      <Modal
        opened={opened}
        onClose={close}
        title={`Report: ${selectedReport}`}
        fullScreen
        radius={0}
        transitionProps={{ transition: "fade", duration: 200 }}
        styles={{
          content: { display: "flex", flexDirection: "column" },
          body: { flex: 1 },
        }}
      >
        <ReportPreview
          reportName={selectedReport}
          onCloseModal={() => {
            close();
            fetchAttachments();
          }}
          reportParameters={{ ChildId: childId, AuthId: authId }}
        />
      </Modal>
    </>
  );
};

export default DocumentStorage;
