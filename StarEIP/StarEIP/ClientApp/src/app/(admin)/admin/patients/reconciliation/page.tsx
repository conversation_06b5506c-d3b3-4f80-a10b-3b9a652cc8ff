"use client";

import React, { useRef, useEffect, useState } from "react";
import {
  Container,
  Title,
  Badge,
  Group,
  Text,
  Tooltip,
  Select,
} from "@mantine/core";
import Button from "devextreme-react/button";
import DataGrid, {
  Column,
  Paging,
  Pager,
  FilterRow,
  HeaderFilter,
  Scrolling,
  Selection,
  Sorting,
  Grouping,
  GroupPanel,
  SearchPanel,
  ColumnChooser,
  Export,
  Toolbar,
  Item,
  DataGridRef,
} from "devextreme-react/data-grid";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { IconSearch, IconRefresh } from "@tabler/icons-react";
import ChildMappingDrawer from "../components/ChildMappingDrawer";

const serviceUrl = urlHelpers.getAbsoluteURL("api/child-reconciliation");

interface ChildReconciliation {
  childId: string;
  childName: string;
  childFirstName?: string;
  childLastName?: string;
  gender?: string;
  primaryLanguage?: string;
  dob: Date;
  guardianFirstName?: string;
  guardianLastName?: string;
  fullAddress?: string;
  existsInHub: number;
  existsInPs: number;
  existsInStar: number;
  hubPsMatch: boolean;
  hubStarMatch: boolean;
  psStarMatch: boolean;
  hubChildId: string;
  psChildId: string;
  starChildId: number;
  starProgramId: string;
  hasActiveAuth: boolean;
  isInAllSystems: boolean;
  hasMismatch: boolean;
  systemCount: number;
}

const ChildReconciliationPage = () => {
  const dataGridRef = useRef<DataGridRef<any, any>>(null);
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const [serviceCoordinators, setServiceCoordinators] = useState<string[]>([]);
  const [selectedServiceCoordinator, setSelectedServiceCoordinator] = useState<
    string | null
  >(null);

  const loadDataSource = async () => {
    let url = serviceUrl;
    if (selectedServiceCoordinator) {
      url += `?serviceCoordinator=${encodeURIComponent(selectedServiceCoordinator)}`;
    }

    setRemoteDataSource(
      createStore({
        key: "rowKey",
        loadUrl: url,
        onBeforeSend: (_, ajaxOptions) => {
          const token = localStorage.getItem("jwtToken");
          if (token) {
            ajaxOptions.headers = {
              ...ajaxOptions.headers,
              Authorization: `Bearer ${token}`,
            };
          }
        },
      }),
    );
  };

  const loadServiceCoordinators = async () => {
    try {
      const token = localStorage.getItem("jwtToken");
      const response = await fetch(
        urlHelpers.getAbsoluteURL(
          "api/child-reconciliation/service-coordinators",
        ),
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.ok) {
        const coordinators = await response.json();
        setServiceCoordinators(coordinators);
      }
    } catch (error) {
      console.error("Error loading service coordinators:", error);
    }
  };

  useEffect(() => {
    loadDataSource();
    loadServiceCoordinators();
  }, []);

  useEffect(() => {
    loadDataSource();
  }, [selectedServiceCoordinator]);

  const handleRefresh = () => {
    loadDataSource();
    loadServiceCoordinators();
    if (dataGridRef.current) {
      dataGridRef.current.instance().refresh();
    }
  };

  const handleServiceCoordinatorChange = (value: string | null) => {
    setSelectedServiceCoordinator(value);
  };

  const renderMatchBadge = (data: any) => {
    if (data.value === null || data.value === undefined) {
      return (
        <Badge color="gray" variant="filled" size="sm">
          N/A
        </Badge>
      );
    }
    const matches = data.value === true;
    return (
      <Badge color={matches ? "green" : "red"} variant="filled" size="sm">
        {matches ? "Match" : "Mismatch"}
      </Badge>
    );
  };

  const renderStarProgramIdCell = (data: any) => {
    const row = data.data as ChildReconciliation;
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
          height: "100%",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <span>{row.childId || "N/A"}</span>
        {row.childId && (
          <Tooltip label="Map to a child" position="right" withArrow>
            <ChildMappingDrawer
              eiNumber={row.childId}
              onMappingSuccess={loadDataSource}
              buttonSize="xs"
              childData={row}
              buttonText={<IconSearch size="0.8rem" />}
            />
          </Tooltip>
        )}
      </div>
    );
  };

  return (
    <DataGrid
      ref={dataGridRef}
      dataSource={remoteDataSource}
      showBorders={true}
      remoteOperations={true}
      allowColumnReordering={true}
      allowColumnResizing={true}
      columnAutoWidth={true}
      height="100%"
      width="100%"
      rowAlternationEnabled
      focusedRowEnabled
      autoNavigateToFocusedRow
    >
      <Paging enabled={true} defaultPageSize={50} />
      <Pager
        visible={true}
        allowedPageSizes={[25, 50, 100, 200]}
        showPageSizeSelector={true}
        showInfo={true}
        showNavigationButtons={true}
        infoText="Page {0} of {1} (Total Rows: {2})"
      />
      <FilterRow visible={true} />
      <HeaderFilter visible={true} />
      <Sorting mode="multiple" />
      <Grouping autoExpandAll={false} />
      <GroupPanel visible={true} />
      <SearchPanel visible={true} width={240} placeholder="Search..." />
      <ColumnChooser enabled={true} />
      <Export enabled={true} />

      <Toolbar>
        <Item name="searchPanel" location="before" />
        <Item location="after">
          <Button onClick={handleRefresh} icon="refresh" />
        </Item>
        <Item location="before">
          <Select
            placeholder="Filter by service coordinator"
            data={serviceCoordinators}
            value={selectedServiceCoordinator}
            onChange={handleServiceCoordinatorChange}
            clearable
            searchable
            style={{ minWidth: 300 }}
          />
        </Item>
        <Item name="groupPanel" />
        <Item name="exportButton" />
        <Item name="columnChooserButton" />
      </Toolbar>

      <Column
        dataField="childId"
        caption="Child ID"
        cellRender={renderStarProgramIdCell}
      />
      <Column dataField="childName" caption="Child Name" />
      <Column dataField="childFirstName" caption="First Name" visible={false} />
      <Column dataField="childLastName" caption="Last Name" visible={false} />
      <Column dataField="gender" caption="Gender" />
      <Column dataField="primaryLanguage" caption="Primary Language" />
      <Column dataField="dob" caption="Date of Birth" dataType="date" />
      <Column
        dataField="guardianFirstName"
        caption="Guardian First Name"
        visible={false}
      />
      <Column
        dataField="guardianLastName"
        caption="Guardian Last Name"
        visible={false}
      />
      <Column dataField="fullAddress" caption="Full Address" visible={false} />
      <Column dataField="existsInHub" caption="In Hub" dataType="boolean" />
      <Column dataField="existsInPs" caption="In PS" dataType="boolean" />
      <Column dataField="existsInStar" caption="In Star" dataType="boolean" />
      <Column
        dataField="hubPsMatch"
        caption="Hub-PS Match"
        cellRender={renderMatchBadge}
      />
      <Column
        dataField="hubStarMatch"
        caption="Hub-Star Match"
        cellRender={renderMatchBadge}
      />
      <Column
        dataField="psStarMatch"
        caption="PS-Star Match"
        cellRender={renderMatchBadge}
      />
      <Column dataField="hasActiveAuth" caption="Has Active Auth" />
      <Column dataField="hubChildId" caption="Hub Child ID" />
      <Column dataField="psChildId" caption="PS Child ID" />
      <Column dataField="starChildId" caption="Star Child ID" />
      <Column dataField="starProgramId" caption="Star Program ID" />
    </DataGrid>
  );
};

export default ChildReconciliationPage;
