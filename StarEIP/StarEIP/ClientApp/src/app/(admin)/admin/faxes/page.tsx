"use client";

import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import DataGrid, {
  <PERSON>um<PERSON>,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
  Button as DgButton,
  DataGridTypes,
  MasterDetail,
  DataGridRef,
} from "devextreme-react/data-grid";
import But<PERSON> from "devextreme-react/button";
import Splitter, { Item as SplitItem } from "devextreme-react/splitter";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import { useRouter } from "next/navigation";
import urlHelpers from "@/app/urlHelpers";
import dataGridExport from "@/app/Utils/dataGridExport";
import axios from "axios";
import { IconOutbound } from "@tabler/icons-react";
import FaxRowDetails from "@/app/(admin)/admin/faxes/FaxRowDetails";

const serviceUrl = urlHelpers.getAbsoluteURL("api/faxes");

const FaxesPage: React.FC = () => {
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const [pdfUrl, setPdfUrl] = useState<string>("");
  const router = useRouter();
  const dataGridRef = useRef<DataGridRef>(null);

  const loadChildDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  };

  useEffect(() => {
    loadChildDataSource();
  }, []);

  const handleFocusedRowChanging = useCallback(
    async (e: DataGridTypes.FocusedRowChangedEvent) => {
      const data = e.row?.data;
      if (!data?.faxId) {
        setPdfUrl("");
        return;
      }
      setTimeout(async () => {
        try {
          const response = await axios.get(`${serviceUrl}/${data.faxId}/pdf`, {
            responseType: "blob",
          });
          const url = URL.createObjectURL(response.data);
          setPdfUrl(url);
        } catch (error) {
          console.error("Error fetching PDF:", error);
        }
      }, 100);
    },
    [],
  );

  const formatPhoneNumber = (phoneNumber: string): string => {
    if (phoneNumber.startsWith("+1") && phoneNumber.length === 12) {
      return `${phoneNumber.substring(2, 5)}-${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8, 12)}`;
    }
    return phoneNumber.startsWith("+")
      ? phoneNumber
      : `${phoneNumber.substring(0, 3)}-${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6, 10)}`;
  };

  const onRowClick = (e: DataGridTypes.RowClickEvent) => {
    if (e.handled) return;
    const key = e.key;
    const dataGrid = dataGridRef?.current?.instance();

    if (dataGrid?.isRowExpanded(key)) {
      dataGrid.collapseRow(key);
    } else {
      dataGrid?.expandRow(key);
    }
  };

  const renderDataGrid = () => {
    return (
      <DataGrid
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        remoteOperations
        onFocusedRowChanged={handleFocusedRowChanging}
        onRowClick={onRowClick}
      >
        <Toolbar>
          <Item location="before">
            <Button icon="refresh" onClick={loadChildDataSource} />
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item name="applyFilterButton" locateInMenu="auto" location="after" />
          <Item name="revertButton" locateInMenu="auto" location="after" />
          <Item name="saveButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <FilterPanel visible />
        <FilterBuilderPopup />
        <Export enabled={true} />
        <Paging defaultPageSize={50} />
        <Pager
          showPageSizeSelector={true}
          allowedPageSizes={[50, 100, 250]}
          showInfo={true}
        />
        <MasterDetail
          enabled
          component={(props) => (
            <FaxRowDetails
              loadData={loadChildDataSource}
              data={props.data.data}
              pdfUrl={pdfUrl}
            />
          )}
        />

        <Column
          dataField="id"
          caption="ID"
          allowEditing={false}
          visible={false}
        />
        <Column dataField="faxId" caption="Fax ID" visible={false} />
        <Column
          dataField="direction"
          width="25px"
          caption=" "
          cellRender={(cellData) =>
            cellData.value === "out" && <IconOutbound height="13px" />
          }
        />
        <Column
          caption="Fax Number"
          calculateCellValue={(data) =>
            formatPhoneNumber(
              data.direction === "in" ? data.fromNumber : data.to,
            )
          }
        />
        <Column dataField="pages" caption="Pages" dataType="number" />
        <Column
          dataField="createdAt"
          caption="Created At"
          dataType="date"
          format="shortDateShortTime"
          sortIndex={0}
          sortOrder="desc"
        />

        <Column
          dataField="downloadUrl"
          caption="Download URL"
          visible={false}
        />
        <Column
          dataField="physicianId"
          caption="Physician ID"
          visible={false}
        />
        <Column
          dataField="physicianName"
          caption="Physician Name"
          visible={true}
        />
      </DataGrid>
    );
  };

  const renderDetails = () => {
    return (
      <object
        style={{ height: "calc(100% - 4px)", width: "100%" }}
        id="myPdfObject"
        data={pdfUrl}
        type="application/pdf"
      >
        <p>
          Alternative text - include a link <a href={pdfUrl}>to the PDF!</a>
        </p>
      </object>
    );
  };

  return (
    <Splitter id="splitter" height="100%">
      <SplitItem
        resizable={true}
        size="50%"
        minSize="70px"
        render={renderDataGrid}
      />
      <SplitItem resizable={true} minSize="250px" render={renderDetails} />
    </Splitter>
  );
};

export default FaxesPage;
