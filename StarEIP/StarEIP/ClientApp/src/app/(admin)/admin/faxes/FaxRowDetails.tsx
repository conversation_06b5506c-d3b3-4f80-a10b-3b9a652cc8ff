import React, { useEffect, useState } from "react";
import axios from "axios";
import { <PERSON>er, Button, Group, Tabs } from "@mantine/core";
import PatientTable from "@/app/(admin)/admin/patients/PatientTable";
import { useDisclosure } from "@mantine/hooks";
import urlHelpers from "@/app/urlHelpers";
import Splitter, { Item as SplitItem } from "devextreme-react/splitter";
import PhysicianCard from "@/app/(admin)/admin/physicians/PhysicianCard";
import PhysicianPage from "../physicians/PhysicianDataGrid";
import NewPatientPage from "../patients/new/page";

const serviceUrl = urlHelpers.getAbsoluteURL("api/physicians");

interface DetailTemplateProps {
  data: {
    physicianId?: string;
    id: number;
  };
  pdfUrl: string;
  loadData: () => void;
}

const DetailTemplate: React.FC<DetailTemplateProps> = ({
  data,
  pdfUrl,
  loadData,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [renderMode, serRenderMode] = useState<string>("");
  const [linkedPhysician, setLinkedPhysician] = useState(null);

  const openDrawer = (renderMode: string) => {
    serRenderMode(renderMode);
    open();
  };

  const fetchLinkedData = async () => {
    if (!data.physicianId) {
      setLinkedPhysician(null);
      return;
    }

    try {
      const physicianResponse = await axios.get(
        `/api/physicians/${data.physicianId}`,
      );
      setLinkedPhysician(physicianResponse.data);
    } catch (error) {
      console.error("Error fetching linked data:", error);
    }
  };

  useEffect(() => {
    let _ = fetchLinkedData();
  }, [data]);

  const handleLinkChild = async (childId?: number) => {
    try {
      await axios.post(`/api/faxes`, { faxId: data.id, childId });
      close();
    } catch (error) {
      console.error("Error linking child:", error);
    }
  };

  const handleLinkPhysician = async (physician: any) => {
    if (physician) {
      try {
        await axios.post(`/api/faxes/LinkPhysician`, {
          faxId: data.id,
          physicianId: physician.id,
        });
        loadData();
        close();
      } catch (error) {
        console.error("Error linking physician:", error);
      }
    }
  };

  const renderPatientTable = () => {
    return (
      <PatientTable
        showToolbar={false}
        item={null}
        onFocusedChildChanging={(child) => handleLinkChild(child?.id)}
      />
    );
  };

  const renderPhysicianDataGrid = () => {
    return (
      <PhysicianPage onRowClick={(e: any) => handleLinkPhysician(e.data)} />
    );
  };

  const renderDetails = () => {
    return (
      <object
        style={{ height: "calc(100% - 4px)", width: "100%" }}
        id="myPdfObject"
        data={pdfUrl}
        type="application/pdf"
      >
        <p>
          Alternative text - include a link <a href={pdfUrl}>to the PDF!</a>
        </p>
      </object>
    );
  };

  return (
    <div onClick={(e) => e.stopPropagation()} style={{ height: "300px" }}>
      <Group>
        <Button onClick={() => openDrawer("add_patient")} size="xs">
          Add Patient
        </Button>
        <Button onClick={() => openDrawer("link_patient")} size="xs">
          Link Patient
        </Button>
        <Button onClick={() => openDrawer("link_physician")} size="xs">
          Link Physician
        </Button>
      </Group>

      <Tabs defaultValue="physician">
        <Tabs.List>
          <Tabs.Tab value="physician">Linked Physician</Tabs.Tab>
          <Tabs.Tab value="patients">Linked Patients</Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="physician">
          {linkedPhysician ? (
            <PhysicianCard physician={linkedPhysician} />
          ) : (
            <p>No linked physician</p>
          )}
        </Tabs.Panel>
      </Tabs>

      <Drawer
        opened={opened}
        onClose={close}
        size="100%"
        padding="xs"
        styles={{
          content: { display: "flex", flexDirection: "column" },
          body: { flex: 1, overflow: "auto" },
        }}
        overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
        closeOnClickOutside={false}
      >
        <Splitter id="splitter" height="100%">
          <SplitItem
            resizable={true}
            size="50%"
            minSize="70px"
            render={() => (
              <>
                {renderMode === "add_patient" && <NewPatientPage />}
                {renderMode === "link_patient" && renderPatientTable()}
                {renderMode === "link_physician" && renderPhysicianDataGrid()}
              </>
            )}
          />
          <SplitItem resizable={true} minSize="250px" render={renderDetails} />
        </Splitter>
      </Drawer>
    </div>
  );
};

export default DetailTemplate;
