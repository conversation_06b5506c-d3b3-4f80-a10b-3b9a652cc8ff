import { Authorization } from "../../../../../../types/Authorization";
import { notifications } from "@mantine/notifications";
import axios from "axios";
import { IconCheck } from "@tabler/icons-react";
import { Group, Select, Stack, Title } from "@mantine/core";
import AuthorizationCard from "@/app/(admin)/admin/authorizations/components/AuthorizationCard";
import React from "react";
import { useAuthorizationStore } from "@/app/(admin)/admin/authorizations/AuthorizationStore";
import urlHelpers from "@/app/urlHelpers";
import AuthorizationCardHeader from "@/app/(admin)/admin/authorizations/components/AuthorizationCardHeader";

interface StatusAuthorizationCard {
  authorization: Authorization;
  statuses: { id: string; name: string }[];
}

const StatusAuthorizationCard: React.FC<StatusAuthorizationCard> = ({
  authorization,
  statuses,
}) => {
  return (
    <Stack py="md" px="xs" gap="xs">
      <AuthorizationCardHeader
        authorization={authorization}
        statuses={statuses}
        showEdit={false}
      />
      <AuthorizationCard authorization={authorization} showStatus={false} />
    </Stack>
  );
};

export default StatusAuthorizationCard;
