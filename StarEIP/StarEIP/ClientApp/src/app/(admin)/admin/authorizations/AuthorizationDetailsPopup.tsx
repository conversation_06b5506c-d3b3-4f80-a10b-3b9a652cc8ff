"use client";
import React, { useState, useRef, useCallback, useEffect } from "react";
import Form, {
  GroupItem,
  Item,
  ButtonItem,
  FormRef,
  FormTypes,
} from "devextreme-react/form";
import { RequiredRule, CustomRule } from "devextreme-react/validator";
import { ButtonType } from "devextreme-react/common";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import {
  Authorization,
  newAuthorizationId,
  AuthorizationStatus,
} from "../../../../../types/Authorization";
import { api } from "@/api/generated";
import { UserDtoSchema } from "@/api/types";

interface AuthorizationDetailsFormProps {
  closeDetails: () => void;
  initialData?: Authorization;
  isEditMode?: boolean;
  childId?: number;
}

type FormInstance = {
  validate: () => { isValid: boolean };
  reset: () => void;
};

type NewAuthorization = Omit<Authorization, "childId"> & {
  childId: number | undefined;
};

const authTypeOptions = [
  { key: "ISC", value: "ISC" },
  { key: "OSC", value: "OSC" },
];

const AuthorizationDetailsPopup: React.FC<AuthorizationDetailsFormProps> = ({
  closeDetails,
  initialData,
  isEditMode = false,
  childId,
}) => {
  const formRef = useRef<FormRef & { instance: FormInstance }>(null);
  const [authorization, setAuthorization] = useState<NewAuthorization>(
    initialData || {
      id: newAuthorizationId,
      childId: childId,
      statusId: "New",
      userId: undefined,
      userName: "",
      userFullName: "",
    },
  );

  const [users, setUsers] = useState<UserDtoSchema[]>([]);
  const [statuses, setStatuses] = useState<AuthorizationStatus[]>([]);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await api.users.getApiUsersAll();
        setUsers(response.data);
        const responseStatuses = await axios.get(
          urlHelpers.getAbsoluteURL("api/authorizations/statuses"),
        );
        setStatuses(responseStatuses.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchUsers();
  }, [childId]);

  function resetForm() {
    formRef.current?.instance()?.reset();
    setAuthorization({
      id: authorization.id,
      childId: childId,
      statusId: "New",
      userId: undefined,
      userName: "",
      userFullName: "",
    });
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) {
      console.error("formRef.current is not defined");
      return;
    }
    const { isValid } = formRef.current.instance().validate();
    if (!isValid) return;

    const url = isEditMode
      ? urlHelpers.getAbsoluteURL(
          `api/authorizations/update/${authorization.id}`,
        )
      : urlHelpers.getAbsoluteURL("api/authorizations/create");

    try {
      const response = await axios.post(url, authorization);
      if (response.status === 200) {
        closeDetails();
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const [resetButtonOptions, setResetButtonOptions] = useState({
    disabled: true,
    icon: "refresh",
    text: "Reset",
    width: "120px",
    onClick: resetForm,
  });

  const submitButtonOptions = {
    text: "Submit",
    icon: "send",
    type: "default" as ButtonType,
    useSubmitBehavior: true,
    width: "120px",
  };

  const onOptionChanged = useCallback(
    (e: FormTypes.OptionChangedEvent) => {
      if (e.name === "isDirty") {
        setResetButtonOptions({ ...resetButtonOptions, disabled: !e.value });
      }
    },
    [resetButtonOptions, setResetButtonOptions],
  );

  return (
    <form onSubmit={handleSubmit}>
      <Form
        ref={formRef}
        formData={authorization}
        labelMode="floating"
        labelLocation="left"
        onOptionChanged={onOptionChanged}
        showValidationSummary={true}
      >
        <GroupItem colCountByScreen={{ sm: 1, md: 2, lg: 2, xl: 2 }}>
          <Item
            dataField="statusId"
            caption="Status"
            editorType="dxSelectBox"
            editorOptions={{
              dataSource: statuses,
              valueExpr: "id",
              displayExpr: "name",
            }}
          >
            <RequiredRule message="Status is required" />
          </Item>
          <Item
            dataField="authType"
            editorType="dxSelectBox"
            editorOptions={{
              items: authTypeOptions,
              valueExpr: "key",
              displayExpr: "value",
            }}
          >
            <RequiredRule message="Authorization Type is required" />
          </Item>
          <Item
            dataField="userId"
            editorType="dxSelectBox"
            caption="Assigned To SC"
            editorOptions={{
              dataSource: users,
              valueExpr: "id",
              displayExpr: (item: UserDtoSchema) =>
                item
                  ? `${item.firstName} ${item.lastName} (${item.email})`
                  : "",
              searchEnabled: true,
            }}
          >
            <RequiredRule message="User is required" />
            <CustomRule
              message="Please select a user"
              validationCallback={(options: { value: string | number }) => {
                const numValue =
                  typeof options.value === "string"
                    ? parseInt(options.value, 10)
                    : options.value;
                return !isNaN(numValue) && numValue > 0;
              }}
            />
          </Item>
          <Item dataField="authNumber" />
          <Item dataField="startDate" editorType="dxDateBox" />
          <Item dataField="endDate" editorType="dxDateBox" />
          <Item
            dataField="units"
            editorType="dxNumberBox"
            editorOptions={{
              min: 0,
              showSpinButtons: true,
              showClearButton: true,
              onValueChanged: (e: { value: number | null }) => {
                setAuthorization((prev) => ({
                  ...prev,
                  units: e.value === null ? undefined : e.value,
                }));
              },
              value: authorization.units ?? null,
            }}
          />
        </GroupItem>

        <GroupItem cssClass="buttons-group" colCount={2} colSpan={2}>
          <ButtonItem buttonOptions={resetButtonOptions} name="Reset" />
          <ButtonItem buttonOptions={submitButtonOptions} />
        </GroupItem>
      </Form>
    </form>
  );
};

export default AuthorizationDetailsPopup;
