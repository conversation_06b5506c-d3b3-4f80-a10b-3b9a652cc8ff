import { DataGridRef } from "devextreme-react/data-grid";
import React, { RefObject } from "react";
import { Authorization } from "../../../../../types/Authorization";
import { create } from "zustand";
import { CustomStore } from "devextreme-aspnet-data-nojquery";

interface AuthorizationStore {
  remoteDataSource: CustomStore | null;
  setRemoteDataSource: (dataSource: CustomStore) => void;
  getRemoteDataSource: () => CustomStore | null;
  dataGridRef: RefObject<DataGridRef> | null;
  setDataGridRef: (ref: any) => void;
  selectedAuthorization: Authorization | null;
  setSelectedAuthorization: (authorization: Authorization | null) => void;
}

export const useAuthorizationStore = create<AuthorizationStore>((set, get) => ({
  remoteDataSource: null,
  setRemoteDataSource: (dataSource: CustomStore) =>
    set({ remoteDataSource: dataSource }),
  getRemoteDataSource: () => get().remoteDataSource,
  dataGridRef: null,
  setDataGridRef: (ref: React.RefObject<DataGridRef> | null) =>
    set({ dataGridRef: ref }),
  selectedAuthorization: null,
  setSelectedAuthorization: (authorization: Authorization | null) =>
    set({ selectedAuthorization: authorization }),
}));
