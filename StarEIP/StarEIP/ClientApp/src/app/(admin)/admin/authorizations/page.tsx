"use client";
import React, { useState } from "react";
import AuthorizationsTable from "./AuthorizationsTable";
import { useMediaQuery } from "@mantine/hooks";
import { Authorization } from "../../../../../types/Authorization";
import { Drawer } from "@mantine/core";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";
import Splitter, { Item } from "devextreme-react/splitter";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { showNotification } from "@/app/Utils/notificationUtils";
import { ChildDetailsDtoSchema } from "@/api/types";
import { useAuthorizationStore } from "@/app/(admin)/admin/authorizations/AuthorizationStore";
import ScAuthUpdate from "./components/ScAuthUpdate";

const AuthorizationsPage: React.FC = () => {
  const { dataGridRef } = useAuthorizationStore();
  const [selectedAuthorization, setSelectedAuthorization] =
    useState<Authorization | null>(null);
  const matches = useMediaQuery("(min-width: 56.25em)");
  const [selectedChild, setSelectedChild] = useState<
    ChildDetailsDtoSchema | null
  >(null);
  const [showChildPanel, setShowChildPanel] = useState(false);

  const handleChildNameClick = async (childId: number) => {
    try {
      const response = await axios.get(
        urlHelpers.getAbsoluteURL(`api/children/${childId}`),
      );
      setSelectedChild(response.data);
      setShowChildPanel(true);
    } catch (error) {
      console.error("Error fetching child details:", error);
      showNotification("error", "Failed to load child details");
    }
  };

  const handleAuthorizationUpdate = (updatedAuthorization: Authorization) => {
    setSelectedAuthorization(updatedAuthorization);
  };

  const handleDataGridRefresh = () => {
    if (dataGridRef && dataGridRef.current) {
      const grid = dataGridRef.current.instance();
      grid.refresh();
    }
  };

  const renderAuthorizationsTable = () => (
    <AuthorizationsTable
      onSelectAuthorizationChange={(authorization) =>
        setSelectedAuthorization(authorization)
      }
      onChildNameClick={handleChildNameClick}
    />
  );

  const renderDetails = () => (
    <ScAuthUpdate
      authorization={selectedAuthorization}
      onAuthorizationUpdate={handleAuthorizationUpdate}
      onDataGridRefresh={handleDataGridRefresh}
    />
  );

  return (
    <>
      <Splitter id="splitter">
        <Item
          resizable={true}
          size="75%"
          minSize="70px"
          render={renderAuthorizationsTable}
        />
        {matches && (
          <Item
            resizable={true}
            minSize="250px"
            render={renderDetails}
            collapsible
          />
        )}
      </Splitter>

      {/* Child Info Side Panel */}
      <Drawer
        opened={showChildPanel}
        onClose={() => setShowChildPanel(false)}
        position="right"
        withCloseButton={false}
        size="lg"
        overlayProps={{ backgroundOpacity: 0.5, blur: 4 }}
      >
        {selectedChild && (
          <ChildInfo child={selectedChild} showAuthorizationsTab={false} />
        )}
      </Drawer>
    </>
  );
};

export default AuthorizationsPage;
