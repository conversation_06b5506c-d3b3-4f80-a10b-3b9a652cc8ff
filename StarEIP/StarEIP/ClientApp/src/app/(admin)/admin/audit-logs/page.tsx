"use client";

import React, { useEffect, useState, useRef } from "react";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
  DataGridRef,
} from "devextreme-react/data-grid";
import Button from "devextreme-react/button";
import dataGridExport from "@/app/Utils/dataGridExport";
import urlHelpers from "@/app/urlHelpers";
import { useHasPermission } from "@/app/store/AuthStore";
import { useRouter } from "next/navigation";

const serviceUrl = urlHelpers.getAbsoluteURL("/api/AuditLogs");

const AuditLogsPage: React.FC = () => {
  const [dataSource, setDataSource] = useState<any>(null);
  const dataGridRef = useRef<DataGridRef>(null);
  const hasViewAuditLogsPermission = useHasPermission("ViewAuditLogs");
  const router = useRouter();

  useEffect(() => {
    if (!hasViewAuditLogsPermission) {
      router.push("/admin/dashboard");
      return;
    }
    loadDataSource();
  }, [hasViewAuditLogsPermission, router]);

  const loadDataSource = () => {
    const ds = createStore({
      key: "id",
      loadUrl: serviceUrl,
      onBeforeSend: (_, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        };
      },
      errorHandler(e) {
        if (e.message === "Unauthorized") {
          window.location.href = "/login";
        }
      },
    });

    setDataSource(ds);
  };

  const refreshGrid = () => {
    loadDataSource();
  };

  const tableCellRender = (data: any) => {
    return (
      <span
        style={{
          backgroundColor: "#e6f7ff",
          padding: "2px 8px",
          borderRadius: "4px",
          display: "inline-block",
        }}
      >
        {data.value}
      </span>
    );
  };

  const usernameCellRender = (data: any) => {
    if (!data.value) return "N/A";

    const fullName = data.data.FullName;
    if (fullName) {
      return (
        <span>
          {data.value} ({fullName})
        </span>
      );
    }

    return data.value;
  };

  return (
    <DataGrid
      ref={dataGridRef}
      dataSource={dataSource}
      showBorders={true}
      remoteOperations={true}
      height="100%"
      width="100%"
      columnAutoWidth={true}
      allowColumnReordering={true}
      allowColumnResizing={true}
      rowAlternationEnabled={true}
      showRowLines={true}
      onExporting={dataGridExport}
    >
      <Toolbar>
        <Item location="before">
          <Button icon="refresh" onClick={refreshGrid} />
        </Item>
        <Item name="searchPanel" locateInMenu="auto" location="before" />
        <Item name="exportButton" locateInMenu="auto" location="after" />
        <Item name="columnChooserButton" locateInMenu="auto" location="after" />
      </Toolbar>
      <RemoteOperations groupPaging={true} />
      <ColumnFixing enabled={true} />
      <SearchPanel visible width={250} />
      <HeaderFilter visible />
      <ColumnChooser enabled />
      <Sorting mode="multiple" />
      <Paging defaultPageSize={20} />
      <Pager
        showPageSizeSelector={true}
        allowedPageSizes={[10, 20, 50, 100]}
        showInfo={true}
      />
      <FilterPanel visible />
      <FilterBuilderPopup />
      <Export enabled={true} />

      <Column
        dataField="id"
        caption="ID"
        dataType="number"
        width={70}
        visible={false}
      />
      <Column
        dataField="timestamp"
        caption="Timestamp"
        dataType="datetime"
        sortOrder="desc"
      />
      <Column
        dataField="tableName"
        caption="Table"
        dataType="string"
        cellRender={tableCellRender}
      />
      <Column dataField="columnName" caption="Column" dataType="string" />
      <Column dataField="primaryKey" caption="Record ID" dataType="number" />
      <Column
        dataField="primaryKeyString"
        caption="Record ID (String)"
        dataType="string"
      />
      <Column dataField="userId" caption="User ID" dataType="number" />
      <Column
        dataField="username"
        caption="Username"
        dataType="string"
        cellRender={usernameCellRender}
      />
      <Column dataField="ipAddress" caption="IP Address" dataType="string" />
      <Column dataField="oldValue" caption="Old Value" dataType="string" />
      <Column dataField="newValue" caption="New Value" dataType="string" />
      <Column dataField="userAgent" caption="User Agent" dataType="string" />
      <Column dataField="jwtGuid" caption="JWT GUID" dataType="string" />
    </DataGrid>
  );
};

export default AuditLogsPage;
