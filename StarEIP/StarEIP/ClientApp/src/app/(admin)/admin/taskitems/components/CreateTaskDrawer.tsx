"use client";

import React, { useState, useEffect } from "react";
import {
  Drawer,
  Button,
  TextInput,
  Select,
  Group,
  ActionIcon,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { useDisclosure } from "@mantine/hooks";
import { IconChecks, IconPlus } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { api } from "@/api/generated";
import { TaskLinkItemSchema, UserDtoSchema } from "@/api/types";

interface CreateTaskDrawerProps {
  linkedItems: TaskLinkItemSchema[];
  onSuccess?: () => void;
  buttonText?: React.ReactNode;
  buttonSize?: "xs" | "sm" | "md" | "lg" | "xl";
  defaultTitle?: string;
  childId?: number | null;
  disabled?: boolean;
  buttonVariant?:
    | "filled"
    | "outline"
    | "light"
    | "white"
    | "default"
    | "subtle"
    | "gradient";
}

const CreateTaskDrawer: React.FC<CreateTaskDrawerProps> = ({
  linkedItems,
  onSuccess,
  buttonText = "Create Task",
  buttonSize = "xs",
  defaultTitle = "",
  childId = null,
  disabled = false,
  buttonVariant = "filled",
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [taskTitle, setTaskTitle] = useState(defaultTitle);
  const [users, setUsers] = useState<UserDtoSchema[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [dueDate, setDueDate] = useState<Date | null>(
    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  ); // 7 days from now
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (opened) {
      loadUsers();
      // Set default title with current date if not provided
      if (!defaultTitle) {
        setTaskTitle(`Task - ${new Date().toLocaleDateString()}`);
      } else {
        setTaskTitle(defaultTitle);
      }
    }
  }, [opened, defaultTitle]);

  const loadUsers = async () => {
    try {
      const response = await api.users.getApiUsersAll();
      setUsers(response.data);
    } catch (error) {
      console.error("Error loading users:", error);
      notifications.show({
        title: "Error",
        message: "Failed to load users",
        color: "red",
      });
    }
  };

  const handleCreateTask = async () => {
    if (!taskTitle.trim()) {
      notifications.show({
        title: "Validation Error",
        message: "Task title is required",
        color: "red",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await api.taskItems.postApiTaskItems({
        linkedItems: linkedItems,
        title: taskTitle,
        taskTemplateId: 1,
        taskStatusId: { update: true, value: 1 },
        assignedToUserId: {
          update: true,
          value: selectedUserId ? parseInt(selectedUserId) : null,
        },
        childId: childId,
        dueAt: { update: true, value: dueDate ? dueDate.toISOString() : null },
      });

      notifications.show({
        title: "Success",
        message: "Task created successfully",
        color: "green",
      });

      close();

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error creating task:", error);
      notifications.show({
        title: "Error",
        message: error.response?.data || "Failed to create task",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if buttonText is a React element (icon)
  const isIcon = React.isValidElement(buttonText);

  return (
    <>
      {isIcon ? (
        <ActionIcon
          onClick={open}
          size={buttonSize}
          disabled={disabled}
          variant={buttonVariant}
        >
          {buttonText}
        </ActionIcon>
      ) : (
        <Button
          onClick={open}
          size={buttonSize}
          disabled={disabled}
          variant={buttonVariant}
          leftSection={<IconPlus size="1rem" />}
        >
          {buttonText}
        </Button>
      )}

      <Drawer
        opened={opened}
        onClose={close}
        position="right"
        size="md"
        padding="md"
        title="Create Task"
        overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
          <TextInput
            label="Task Title"
            placeholder="Enter task title"
            value={taskTitle}
            onChange={(e) => setTaskTitle(e.target.value)}
            required
          />
          <Select
            label="Assign To"
            placeholder="Select a user"
            data={users.map((user) => ({
              value: user.id?.toString() || "",
              label:
                user.firstName && user.lastName
                  ? `${user.firstName} ${user.lastName}`
                  : user.email || `User ${user.id}`,
            }))}
            value={selectedUserId}
            onChange={setSelectedUserId}
            clearable
          />
          <DateInput
            label="Due Date"
            placeholder="Select due date"
            value={dueDate}
            onChange={(value) => setDueDate(value as unknown as Date | null)}
            clearable
            valueFormat="YYYY-MM-DD"
          />
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={close}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateTask}
              loading={isSubmitting}
              leftSection={<IconChecks size="1rem" />}
            >
              Create Task
            </Button>
          </Group>
        </div>
      </Drawer>
    </>
  );
};

export default CreateTaskDrawer;
