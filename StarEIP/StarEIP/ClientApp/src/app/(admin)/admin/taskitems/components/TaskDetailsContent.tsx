"use client";

import React, { useState, useEffect } from "react";
import { Stack, Group, Text, Divider, Select, Tabs } from "@mantine/core";
import {
  IconUser,
  IconClipboardList,
  IconClock,
  IconAdjustments,
  IconBabyCarriage,
  IconLink,
  IconMail,
} from "@tabler/icons-react";
import DataGrid, {
  Column,
  SearchPanel,
  HeaderFilter,
  Paging,
  Pager,
} from "devextreme-react/data-grid";
import { api } from "@/api/generated";
import {
  ChildDetailsDtoSchema,
  FullTaskDetailDtoSchema,
  TaskDetailsDtoSchema,
  TaskItemLinkSchema,
  TaskStatusSchema,
  UserDtoSchema,
} from "@/api/types";
import { NotesSection } from "@/app/(admin)/admin/notes/NotesSection";
import { showNotification } from "@/app/Utils/notificationUtils";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";

interface TaskDetailsContentProps {
  fullTaskDetails: FullTaskDetailDtoSchema | null;
  isLoading?: boolean;
  onTaskUpdated?: (updatedTask?: FullTaskDetailDtoSchema) => void;
  useAbsoluteUrls?: boolean;
  showEmailModal?: boolean;
  showTitle?: boolean;
}

interface TaskUpdatePayload {
  assignedToUserId?: number | null;
  taskStatusId?: number;
}

const TaskDetailsContent: React.FC<TaskDetailsContentProps> = ({
  fullTaskDetails,
  isLoading = false,
  onTaskUpdated,
  showTitle = true,
}) => {
  const [users, setUsers] = useState<UserDtoSchema[]>([]);
  const [statuses, setStatuses] = useState<TaskStatusSchema[]>([]);
  const taskDetails = fullTaskDetails?.taskDetailsDto;

  useEffect(() => {
    const loadData = async () => {
      try {
        const [usersResponse, statusResponse] = await Promise.all([
          api.users.getApiUsersAll(),
          api.taskItems.getApiTaskItemsStatuses(),
        ]);
        setUsers(usersResponse.data);
        setStatuses(statusResponse.data);
      } catch (error) {
        console.error("Error loading users/statuses:", error);
      }
    };
    loadData();
  }, []);

  const handleTaskUpdate = async (updated: TaskUpdatePayload) => {
    if (!taskDetails?.id) return;

    const payload: any = {};

    if (
      updated.assignedToUserId?.toString() !==
      taskDetails.assignedToUserId?.toString()
    ) {
      payload.assignedToUserId = {
        update: true,
        value: updated.assignedToUserId ?? null,
      };
    }

    const currentStatusId = statuses
      .find((s) => s.name === taskDetails.status)
      ?.id?.toString();

    if (
      updated.taskStatusId &&
      updated.taskStatusId?.toString() !== currentStatusId
    ) {
      payload.taskStatusId = {
        update: true,
        value: updated.taskStatusId!,
      };
    }

    if (Object.keys(payload).length === 0) return;

    try {
      const response = await api.taskItems.putApiTaskItemsTaskId(
        taskDetails.id,
        payload,
      );
      if (onTaskUpdated) {
        onTaskUpdated(response.data);
      }
      showNotification("success", "Task updated successfully");
    } catch (error) {
      console.error("Error updating task:", error);
      showNotification("error", "Failed to update task");
    }
  };

  const renderAssignmentSection = () => {
    return (
      <div>
        <Divider my="sm" />
        <Group align="flex-start" justify="stretch" mb="xs" wrap="wrap">
          <Select
            placeholder="Select status"
            data={statuses.map((status) => ({
              value: status.id!.toString(),
              label: status.name || "",
            }))}
            value={
              statuses
                .find((s) => s.name === taskDetails?.status)
                ?.id?.toString() ?? null
            }
            onChange={(value) => {
              if (value) {
                handleTaskUpdate({ taskStatusId: parseInt(value) });
              }
            }}
            searchable
            leftSection={<IconAdjustments size={16} />}
            style={{ flex: "1 1 150px", minWidth: 150 }}
          />
          <Select
            placeholder="Select a user"
            data={users.map((user) => ({
              value: user.id?.toString() || "",
              label: `${user.firstName} ${user.lastName}`,
            }))}
            value={taskDetails?.assignedToUserId?.toString() ?? null}
            onChange={(value) => {
              handleTaskUpdate({
                assignedToUserId: value ? parseInt(value) : null,
              });
            }}
            clearable
            searchable
            leftSection={<IconUser size={16} />}
            style={{ flex: "1 1 150px", minWidth: 150 }}
          />
        </Group>
      </div>
    );
  };

  if (isLoading) {
    return <Text>Loading task details...</Text>;
  }

  if (!taskDetails) {
    return <Text>No task selected</Text>;
  }

  return (
    <Stack
      gap="1"
      style={{
        height: "100%",
        overflowY: "auto",
        width: "100%",
      }}
    >
      {showTitle && (
        <Text>
          Task #{taskDetails.id}: {taskDetails.title}
        </Text>
      )}
      {renderAssignmentSection()}
      <Tabs
        defaultValue="details"
        id="taskDetailsTab"
        styles={{
          root: {
            display: "flex",
            flexDirection: "column",
            flex: 1,
            overflow: "auto",
            width: "100%",
          },
        }}
      >
        <Tabs.List>
          <Tabs.Tab
            value="details"
            leftSection={<IconClipboardList size={16} />}
          >
            Details
          </Tabs.Tab>
          <Tabs.Tab value="notes" leftSection={<IconClock size={16} />}>
            Notes
          </Tabs.Tab>
          <Tabs.Tab value="links" leftSection={<IconLink size={16} />}>
            Linked Items
          </Tabs.Tab>
          <Tabs.Tab value="emails" leftSection={<IconMail size={16} />}>
            Emails
          </Tabs.Tab>
          <Tabs.Tab value="child" leftSection={<IconBabyCarriage size={16} />}>
            Child
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="details">
          <Stack gap="md">
            <div style={{ marginBottom: "20px" }}>
              <Text size="xl" fw={600}>
                {taskDetails.title}
              </Text>
              {fullTaskDetails.childDetailsDto && (
                <Text size="md" c="dimmed">
                  Child:{" "}
                  {fullTaskDetails.childDetailsDto.patientName ||
                    `${fullTaskDetails.childDetailsDto.firstName || ""} ${fullTaskDetails.childDetailsDto.lastName || ""}`.trim() ||
                    "Unknown"}
                </Text>
              )}
            </div>
            <Group grow mb="md" wrap="wrap">
              <Text fw={500} mb="xs">
                Created At
              </Text>
              <Text>
                {taskDetails.createdAt
                  ? new Date(taskDetails.createdAt).toLocaleString()
                  : "N/A"}
              </Text>

              <Text fw={500} mb="xs">
                Due At
              </Text>
              <Text>
                {taskDetails.dueAt
                  ? new Date(taskDetails.dueAt).toLocaleString()
                  : "No due date"}
              </Text>
            </Group>
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel
          value="notes"
          style={{
            height: "100%",
            display: "flex",
            flex: 1,
            flexGrow: 1,
            flexDirection: "column",
            width: "100%",
          }}
        >
          {taskDetails.id && (
            <NotesSection
              entityType="task"
              entityId={taskDetails.id}
              maxLength={500}
              enableDelete={true}
            />
          )}
        </Tabs.Panel>

        <Tabs.Panel
          value="links"
          style={{
            height: "100%",
            display: "flex",
            flex: 1,
            flexGrow: 1,
            flexDirection: "column",
            width: "100%",
          }}
        >
          <DataGrid
            dataSource={fullTaskDetails.taskItemLinks}
            showBorders={true}
            columnAutoWidth={true}
            showColumnLines={false}
            rowAlternationEnabled={true}
            height="100%"
            width="100%"
          >
            <HeaderFilter visible />
            <Paging defaultPageSize={10} />
            <Pager showPageSizeSelector />

            <Column
              dataField="linkType"
              caption="Type"
              calculateCellValue={(data) => data.linkType || "Generic"}
            />
            <Column dataField="linkId" caption="ID" />
            <Column dataField="linkTable" caption="Table" />
            <Column
              dataField="createdAt"
              caption="Created At"
              dataType="datetime"
              format="shortDateShortTime"
            />
          </DataGrid>
        </Tabs.Panel>

        <Tabs.Panel
          value="emails"
          style={{
            height: "100%",
            display: "flex",
            flex: 1,
            flexGrow: 1,
            flexDirection: "column",
            width: "100%",
          }}
        >
          <DataGrid
            dataSource={fullTaskDetails.emails}
            showBorders={true}
            columnAutoWidth={true}
            showColumnLines={false}
            rowAlternationEnabled={true}
            height="100%"
            width="100%"
          >
            <HeaderFilter visible />
            <Paging defaultPageSize={10} />
            <Pager showPageSizeSelector />

            <Column dataField="emailMessage.subject" caption="Subject" />
            <Column dataField="emailMessage.senderEmail" caption="From" />
            <Column
              dataField="emailMessage.receivedAt"
              caption="Received At"
              dataType="datetime"
              format="shortDateShortTime"
            />
            <Column
              dataField="linkType"
              caption="Link Type"
              calculateCellValue={(data) => {
                switch (data.linkType) {
                  case 0:
                    return "Created Task";
                  case 1:
                    return "Created From Task";
                  case 2:
                    return "Linked";
                  default:
                    return "Unknown";
                }
              }}
            />
            <Column
              caption="View in Outlook"
              cellRender={(data) => {
                if (
                  data.data &&
                  data.data.emailMessage &&
                  data.data.emailMessage.webLink
                ) {
                  return (
                    <a
                      href={data.data.emailMessage.webLink}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Open
                    </a>
                  );
                }
                return null;
              }}
            />
          </DataGrid>
        </Tabs.Panel>

        <Tabs.Panel
          value="child"
          style={{
            height: "100%",
            display: "flex",
            flex: 1,
            flexGrow: 1,
            flexDirection: "column",
            width: "100%",
          }}
        >
          <ChildInfo
            child={fullTaskDetails.childDetailsDto || null}
            showAuthorizationsTab
          />
        </Tabs.Panel>
      </Tabs>
    </Stack>
  );
};

export default TaskDetailsContent;
