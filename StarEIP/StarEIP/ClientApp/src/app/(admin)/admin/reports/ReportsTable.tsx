"use client";
import React, { useEffect, useRef, useState, useCallback } from "react";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  <PERSON>umn,
  <PERSON>r,
  Paging,
  FilterRow,
  HeaderFilter,
  SearchPanel,
  <PERSON><PERSON>bar,
  Item,
} from "devextreme-react/data-grid";
import { useRouter } from "next/navigation";
import urlHelpers from "../../../urlHelpers";
import Link from "next/link";
import { Button as DxButton } from "devextreme-react/button";
import { CheckBox } from "devextreme-react/check-box";

const serviceUrl = urlHelpers.getAbsoluteURL("api/reports");

const ReportsTable: React.FC = () => {
  const [dataSource, setDataSource] = useState<any>(null);
  const router = useRouter();
  const dataGridRef = useRef(null);

  const loadReportDataSource = useCallback(() => {
    const ds = createStore({
      key: "id",
      loadUrl: serviceUrl,
      onBeforeSend: (e, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        };
      },
      errorHandler: (e) => {
        if (e.message === "Unauthorized") {
          router.push("/login");
        }
      },
    });
    setDataSource(ds);
  }, [router]);

  useEffect(() => {
    loadReportDataSource();
  }, [loadReportDataSource]);

  const renderOpenLink = (cellData: any) => {
    return <Link href={`/admin/reports/${cellData.data.name}`}>Open</Link>;
  };

  // Replace the two separate handler functions with a single generic handler
  const handleFieldChange = async (
    fieldName: string,
    id: number,
    value: boolean,
  ) => {
    try {
      const response = await fetch(`${serviceUrl}/${id}/updateFields`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        },
        body: JSON.stringify({ [fieldName]: value }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update ${fieldName}`);
      }

      // Refresh the DataGrid
      loadReportDataSource();
    } catch (error) {
      console.error(`Error updating ${fieldName}:`, error);
      // Optionally, display an error message to the user
    }
  };

  return (
    <DataGrid
      ref={dataGridRef}
      dataSource={dataSource}
      showBorders={true}
      remoteOperations={true}
      allowColumnReordering={true}
      rowAlternationEnabled={true}
      height="100%"
      width="100%"
      allowColumnResizing={true}
      columnAutoWidth={true}
      columnResizingMode={"widget"}
      showColumnLines={false}
      twoWayBindingEnabled
      focusedRowEnabled
      autoNavigateToFocusedRow
    >
      <FilterRow visible={false} />
      <HeaderFilter visible={false} />
      <SearchPanel visible={true} width={240} placeholder="Search..." />

      <Column dataField="id" caption="ID" width={100} />
      <Column dataField="name" caption="Name" />
      <Column dataField="displayName" caption="Display Name" />
      <Column
        dataField="showOnChildDetails"
        caption="Show On Child Details"
        dataType="boolean"
        width={170}
        cellRender={(cellData) => (
          <CheckBox
            value={cellData.value}
            onValueChanged={(e) =>
              handleFieldChange("showOnChildDetails", cellData.data.id, e.value)
            }
          />
        )}
      />
      <Column
        dataField="showOnAuthDetails"
        caption="Show On Auth Details"
        dataType="boolean"
        width={170}
        cellRender={(cellData) => (
          <CheckBox
            value={cellData.value}
            onValueChanged={(e) =>
              handleFieldChange("showOnAuthDetails", cellData.data.id, e.value)
            }
          />
        )}
      />
      <Column caption="Actions" cellRender={renderOpenLink} width={100} />

      <Paging defaultPageSize={20} />
      <Pager showPageSizeSelector={true} allowedPageSizes={[10, 20, 50]} />

      <Toolbar>
        <Item location="before">
          <DxButton icon="refresh" onClick={loadReportDataSource} />
        </Item>
        <Item name="searchPanel" />
      </Toolbar>
    </DataGrid>
  );
};

export default ReportsTable;
