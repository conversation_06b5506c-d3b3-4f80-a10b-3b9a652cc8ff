import React, { useRef } from "react";
import urlHelpers from "@/app/urlHelpers";
import Report<PERSON>iewer, {
  Callbacks,
  DxReportViewerRef,
  RequestOptions,
} from "devexpress-reporting-react/dx-report-viewer";
import emailIcon from "/public/StarLogo.png"; // Import the image
import { notifications } from "@mantine/notifications";
import { json } from "stream/consumers";

interface ReportParameters {
  ChildId: number;
  AuthId?: number;
  ProviderId?: number;
}

interface ReportPreviewProps {
  reportParameters?: ReportParameters;
  reportName: string;
  onCloseModal?: () => void;
}

const ReportPreview: React.FC<ReportPreviewProps> = ({
  reportName,
  reportParameters,
  onCloseModal,
}) => {
  const viewerRef = useRef<DxReportViewerRef>(null);

  const onParametersReset = ({ sender }: { sender: any }) => {
    var preview = sender.GetPreviewModel();
    if (preview) {
      preview.tabPanel.collapsed = true;
    }
  };

  const onParametersInitialized = ({ args }: { args: any }) => {
    const reportviewer = viewerRef?.current?.instance();
    if (reportviewer) {
      // args.ActualParametersInfo.forEach((parameterModel: any) => {
      //     console.log("Parameter " + parameterModel.parameterDescriptor.name + " value " + JSON.stringify(parameterModel.parameterDescriptor.value));
      // });
      if (reportParameters) {
        Object.keys(reportParameters).forEach((key) => {
          args.ParametersModel[key] =
            reportParameters[key as keyof ReportParameters];
        });
      }
      args.ShouldRequestParameters = false;
      reportviewer?.previewModel?.StartBuild();
    }
  };

  const onDocumentReady = (event: any): void => {
    setTimeout(() => {
      event.sender.GetReportPreview().tabPanel.collapsed = true;
    }, 1000);
    event.sender.GetReportPreview().zoom = 1.25;
    event.sender.GetReportPreview().editingFieldsHighlighted = true;
  };

  const onCustomizeMenuActions = (event: any): void => {
    var sendViaEmailItem = {
      id: "emailCommandId",
      text: "Save to Document Storage",
      imageClassName: "upload-report-icon",
      adaptivePriority: 1,
      visible: true,
      disabled: false,
      clickAction: async function () {
        const reportviewer = viewerRef?.current?.instance();
        if (reportviewer) {
          notifications.show({
            message: "Saving report to Document Storage...",
            color: "blue",
          });
          await reportviewer.PerformCustomDocumentOperation(
            JSON.stringify(reportParameters),
            false,
          );
          if (onCloseModal) {
            onCloseModal();
          }
        }
      },
    };
    event.args.Actions.push(sendViaEmailItem);
  };

  return (
    <ReportViewer reportUrl={reportName} height="100%" ref={viewerRef}>
      <RequestOptions
        host={urlHelpers.getAbsoluteURL("/")}
        invokeAction="DXXRDV"
      />
      <Callbacks
        ParametersInitialized={onParametersInitialized}
        ParametersReset={onParametersReset}
        DocumentReady={onDocumentReady}
        CustomizeMenuActions={onCustomizeMenuActions}
      />
    </ReportViewer>
  );
};

export default ReportPreview;
