"use client";

import { useRouter } from "next/navigation";
import {
  UnstyledButton,
  Tooltip,
  Box,
  Group,
  Divider,
  em,
} from "@mantine/core";
import {
  IconChevronRight,
  IconChevronLeft,
  IconLogout,
} from "@tabler/icons-react";
import NavigationMenu from "@/app/(admin)/components/NavigationMenu";
import { useMainStore } from "@/app/store/MainStore";

const Navigation = () => {
  const router = useRouter();
  const collapsed = useMainStore((state) => state.sideMenuCollapsed);
  const setCollapsed = useMainStore((state) => state.setSideMenuCollapsed);

  return (
    <Box
      id="navigation_box"
      style={{
        backgroundColor: "#1e88e5",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        minWidth: collapsed ? "60px" : "200px", // Set minimum width
      }}
    >
      <Box style={{ flex: 1, overflow: "auto" }}>
        <NavigationMenu />
      </Box>

      <Divider />
      <Group
        justify={collapsed ? "center" : "space-between"}
        align="center"
        p="xs"
        wrap={collapsed ? "wrap" : "nowrap"}
      >
        <Tooltip label="Logout" position="top">
          <UnstyledButton
            style={{
              color: "white",
              padding: "12px",
              width: "100%",
              display: "flex",
              justifyContent: collapsed ? "center" : "flex-start",
              alignItems: "center",
            }}
            onClick={(e: any) => {
              e.preventDefault();
              localStorage.removeItem("jwtToken");
              router.replace("/login");
              console.log("Logout clicked");
            }}
          >
            <IconLogout size="1.5rem" />
            {!collapsed && <span style={{ marginLeft: "10px" }}>Logout</span>}
          </UnstyledButton>
        </Tooltip>
        <Tooltip label={collapsed ? "Expand" : "Collapse"} position="right">
          <UnstyledButton
            onClick={() => setCollapsed(!collapsed)}
            style={{
              width: "100%",
              display: "flex",
              color: "white",
              justifyContent: collapsed ? "center" : "flex-end",
              alignItems: "center",
            }}
          >
            {collapsed ? (
              <IconChevronRight size="1.5rem" />
            ) : (
              <IconChevronLeft size="1.5rem" />
            )}
          </UnstyledButton>
        </Tooltip>
      </Group>
    </Box>
  );
};

export default Navigation;
