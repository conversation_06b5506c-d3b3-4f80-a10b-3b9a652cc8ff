"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Container,
  Loader,
  Paper,
  PasswordInput,
  Stack,
  Text,
  TextInput,
  Title,
} from "@mantine/core";
import urlHelpers from "@/app/urlHelpers";
import {
  IconAlertCircle,
  IconAt,
  IconCheck,
  IconLock,
  IconLogin,
} from "@tabler/icons-react";

const serviceUrl = urlHelpers.getAbsoluteURL("api/auth");

const ConfirmEmailPage = () => {
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState("");
  const [error, setError] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [confirmed, setConfirmed] = useState(false);

  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const token = searchParams.get("token");
    const email = searchParams.get("email");

    if (token && email && !confirmed) {
      const confirmEmail = async () => {
        try {
          const response = await axios.post(`${serviceUrl}/confirm-email`, {
            token,
            email,
          });

          if (response.status === 200) {
            setMessage("Email confirmed successfully!");
            setConfirmed(true);
          } else {
            setMessage("Email confirmation failed. Please try again later.");
            setError(true);
          }
        } catch (error) {
          setError(true);
          setMessage("Email confirmation failed. Please try again later.");
        } finally {
          setLoading(false);
        }
      };

      confirmEmail().catch((error) => {
        console.log(error);
      });
    } else {
      setMessage("Invalid confirmation link.");
      setError(true);
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    let countdownInterval: NodeJS.Timeout | undefined;

    if (!loading && !error && countdown > 0) {
      countdownInterval = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    }

    if (countdown === 0) {
      clearInterval(countdownInterval);
      handleRedirect();
    }

    return () => clearInterval(countdownInterval);
  }, [countdown, loading, error]);

  const handleRedirect = () => {
    const token = searchParams.get("token");
    const email = searchParams.get("email");
    router.push(`/account/set-password?email=${email}&token=${token}`);
  };

  return (
    <Container size={500} my={40}>
      <Title ta="center" fw={900}>
        Email Confirmation
      </Title>
      <Text c="dimmed" size="md" ta="center" mt={5}>
        We&apos;re verifying your email address.
      </Text>

      <Stack mt={30} align="center">
        {loading ? (
          <Loader size="lg" />
        ) : error ? (
          <Alert
            icon={<IconAlertCircle size="1.1rem" />}
            title="Error"
            color="red"
          >
            {message}
          </Alert>
        ) : (
          <>
            <Alert
              icon={<IconCheck size="1.1rem" />}
              title="Success"
              color="green"
            >
              {message}
            </Alert>
            <Text size="sm">
              Redirecting in {countdown} second{countdown > 1 && "s"}...
            </Text>
            <Button
              variant="contained"
              color="primary"
              onClick={handleRedirect}
            >
              Proceed Now
            </Button>
          </>
        )}
      </Stack>
    </Container>
  );
};

export default ConfirmEmailPage;
