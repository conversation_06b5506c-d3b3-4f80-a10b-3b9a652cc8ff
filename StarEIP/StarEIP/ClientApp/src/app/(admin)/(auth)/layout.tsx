"use client";

import React from "react";
import {
  AppShell,
  Box,
  Flex,
  Text,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import Image from "next/image";
import StarBanner from "../../../../public/StarBanner.png";
import { useMediaQuery } from "@mantine/hooks";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const matches = useMediaQuery("(min-width: 56.25em)");

  return (
    <AppShell
      padding={0}
      layout="alt"
      styles={{
        main: {
          backgroundColor:
            colorScheme === "dark"
              ? theme.colors.dark[8]
              : theme.colors.gray[0],
        },
      }}
    >
      <Flex direction="row" gap={2} style={{ height: "100vh" }}>
        <Flex
          style={{
            width: "50%",
            position: "relative",
            display: matches ? "inherit" : "none",
          }}
        >
          <Image
            src={StarBanner}
            alt="Star Banner"
            layout="fill"
            objectFit="cover"
            priority
          />
          <Box
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "rgba(0,0,0,0.5 )",
            }}
          />
          <Box
            style={{
              position: "absolute",
              bottom: 20,
              right: 0,
              left: 0,
            }}
          >
            <Text c="dimmed" size="sm" ta="center">
              © {new Date().getFullYear()} Star EIP. All rights reserved.
            </Text>
          </Box>
        </Flex>
        <Flex
          style={{
            width: matches ? "50%" : "100%",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {children}
        </Flex>
      </Flex>
    </AppShell>
  );
};

export default AuthLayout;
