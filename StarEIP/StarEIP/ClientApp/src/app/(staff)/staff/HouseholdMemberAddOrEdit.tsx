"use client"; // This is a client component

import * as React from "react";
import { Drawer } from "@mui/material";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import HouseholdMemberForm from "./HouseholdMemberForm";
import { Household } from "../../../../types/Household";

interface HouseholdMemberAddOrEditProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  initialHousehold: Household;
}

const HouseholdMemberAddOrEdit = ({
  isOpen,
  onClose,
  onSave,
  initialHousehold,
}: HouseholdMemberAddOrEditProps) => {
  const theme = useTheme();
  const isTabletOrBigger = useMediaQuery(theme.breakpoints.up("sm"));
  const [household, setHousehold] = React.useState<Household>({
    id: initialHousehold?.id,
    key:
      initialHousehold?.id?.toString() ||
      initialHousehold?.key ||
      Date.now().toString(),
    relationship: initialHousehold?.relationship || "",
    lastName: initialHousehold?.lastName || "",
    maidenName: initialHousehold?.maidenName || "",
    firstName: initialHousehold?.firstName || "",
    gender: initialHousehold?.gender || "",
    dateOfBirth: initialHousehold?.dateOfBirth || "",
  });

  React.useEffect(() => {
    setHousehold({
      id: initialHousehold?.id,
      key:
        initialHousehold?.id?.toString() ||
        initialHousehold?.key ||
        Date.now().toString(),
      relationship: initialHousehold?.relationship || "",
      lastName: initialHousehold?.lastName || "",
      maidenName: initialHousehold?.maidenName || "",
      firstName: initialHousehold?.firstName || "",
      gender: initialHousehold?.gender || "",
      dateOfBirth: initialHousehold?.dateOfBirth || "",
    });
  }, [initialHousehold]);

  return (
    <Drawer
      open={isOpen}
      onClose={onClose}
      anchor="right"
      SlideProps={{
        direction: "left",
        timeout: { enter: 650, exit: 300, appear: 1 },
      }}
      sx={{ width: "100vw" }}
    >
      <div
        style={{
          width: isTabletOrBigger ? "500px" : "85vw",
          margin: "20px",
          height: "100%",
        }}
      >
        <HouseholdMemberForm
          household={household}
          isPersonal={false}
          onSave={onSave}
          onClose={onClose}
        />
      </div>
    </Drawer>
  );
};

export default HouseholdMemberAddOrEdit;
