"use client"; // This is a client component
import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { usePlacesWidget } from "react-google-autocomplete";
import TextBox from "devextreme-react/text-box";
import { FocusInEvent } from "devextreme/ui/text_box";
import { TextBoxRef } from "devextreme-react/text-box";

interface AddressInputProps {
  onAddressSelected: (address: SelectedAddress) => void;
  useStreetAddress?: boolean;
  value?: string | undefined;
  onValueChanged: (value: string) => void;
}

export interface SelectedAddress {
  streetAddress: string;
  city: string;
  state: string;
  zip: string;
  formattedAddress: string;
}

const AddressInput = ({
  value,
  onValueChanged,
  onAddressSelected,
  useStreetAddress,
}: AddressInputProps) => {
  const inputRef = useRef<TextBoxRef>(null);
  const { ref } = usePlacesWidget({
    apiKey: "AIzaSyAUBhC3XGtFGKemZ_RnxUqHrN1q4SeYLzo",
    onPlaceSelected: (place) => {
      if (!onAddressSelected) {
        return false;
      }
      const addressComponents = place?.address_components;

      const selectedAddress: SelectedAddress = {
        streetAddress: "",
        city: "",
        state: "",
        zip: "",
        formattedAddress: place.formatted_address,
      };

      addressComponents?.forEach((component: any) => {
        const types = component.types;
        if (types.includes("street_number")) {
          selectedAddress.streetAddress = `${component.long_name} ${selectedAddress.streetAddress}`;
        }
        if (types.includes("route")) {
          selectedAddress.streetAddress += ` ${component.long_name}`;
        }
        if (
          types.includes("sublocality_level_1") ||
          types.includes("locality")
        ) {
          selectedAddress.city = component.long_name;
        }
        if (types.includes("administrative_area_level_1")) {
          selectedAddress.state = component.short_name;
        }
        if (types.includes("postal_code")) {
          selectedAddress.zip = component.long_name;
        }
      });

      // Use the new prop to determine which address to set
      if (useStreetAddress) {
        onValueChanged(selectedAddress.streetAddress);
      } else {
        onValueChanged(selectedAddress.formattedAddress);
      }

      console.log("AddressInput, calling onAddressSelected", selectedAddress);
      onAddressSelected(selectedAddress);
    },
    options: {
      types: ["address"],
    },
  });

  useEffect(() => {
    // Create a style element
    const style = document.createElement("style");
    style.innerHTML = `
            .pac-container {
                z-index: 10000 !important;
            }
        `;
    // Append the style to the document head
    document.head.appendChild(style);

    // Cleanup function to remove the style when component unmounts
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useLayoutEffect(() => {
    if (inputRef.current) {
      const inputElement: HTMLInputElement | null = inputRef.current
        ?.instance()
        .element()
        .querySelector(".dx-texteditor-input");
      // @ts-ignore
      ref.current = inputElement;
    }
  }, [ref, inputRef]);

  const inputAttr = { id: "inputId", autoComplete: "new-pasword" };
  return (
    <>
      {/* Hidden input to catch Chrome's autofill */}
      <input
        type="text"
        style={{ display: "none" }}
        name="fake-address"
        autoComplete="off"
        placeholder="Address"
        aria-label="Address"
      />
      <input
        type="text"
        style={{ display: "none" }}
        name="fake-address-apt"
        autoComplete="off"
        placeholder="Address APT"
        aria-label="Address APT"
      />
      <TextBox
        value={value}
        label="Address"
        placeholder="Enter an Address"
        inputAttr={inputAttr}
        onValueChanged={(e) => onValueChanged(e.value)}
        ref={inputRef}
        onFocusIn={(e: FocusInEvent) => {
          try {
            const inputElement: HTMLInputElement | null = e.component
              .element()
              .querySelector(".dx-texteditor-input");
            if (inputElement) {
              inputElement.select();
            }
          } catch (e) {
            console.log("Error selecting input", e);
          }
        }}
      />
    </>
  );
};

export default AddressInput;
