import React from "react";
import { Card, CardContent, Typography, Box, Checkbox } from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { inter } from "../../fonts";

interface InfoCardProps {
  title: string;
  subtitle: string;
  isComplete: boolean;
  children: React.ReactNode;
}

const InfoCard = ({ title, subtitle, isComplete, children }: InfoCardProps) => {
  return (
    <Card
      sx={{
        margin: "0 auto",
        mt: 3,
        border: "1px solid #ccc",
        borderRadius: "4px",
      }}
    >
      <CardContent>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Box>
            <Typography
              variant="h3"
              component="div"
              color={isComplete ? "success.main" : undefined}
            >
              {title}
            </Typography>
            <Typography variant="caption">{subtitle}</Typography>
          </Box>
          {isComplete && (
            <Checkbox
              color="success"
              checkedIcon={<CheckCircleOutlineIcon />}
              checked={true}
              readOnly
            />
          )}
        </Box>
        {children}
      </CardContent>
    </Card>
  );
};

export default InfoCard;
