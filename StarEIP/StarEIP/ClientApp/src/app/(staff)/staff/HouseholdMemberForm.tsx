import * as React from "react";
import Form, { Item, SimpleItem } from "devextreme-react/form";
import Validator, { RequiredRule } from "devextreme-react/validator";
import {
  Box,
  Button,
  IconButton,
  Stack,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Household } from "../../../../types/Household";
import SaveIcon from "@mui/icons-material/Save";
import TextBox, { TextBoxTypes } from "devextreme-react/text-box";
import DeleteIcon from "@mui/icons-material/Clear";

interface HouseholdMemberFormProps {
  isPersonal: boolean;
  onClose: () => void;
  onSave: (houseHold: Household, remove: boolean) => void;
  household: Household;
}

const HouseholdMemberForm = ({
  isPersonal,
  household,
  onSave,
  onClose,
}: HouseholdMemberFormProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const formRef = React.useRef(null);
  const [maidenNames, setMaidenNames] = React.useState<string[]>([]);

  const countryEditorOptions = {
    dataSource: [
      "APPLICANT",
      "MAIDEN/ALIAS",
      "Wife",
      "Husband",
      "Son",
      "Daughter",
      "Father",
      "Mother",
      "Brother",
      "Sister",
      "Friend",
      "Roommate",
    ],
  };

  const genderOptions = {
    items: [
      { value: "M", text: "Male" },
      { value: "F", text: "Female" },
    ],
    valueExpr: "value",
    displayExpr: "text",
    layout: "horizontal",
  };

  const birthDateOptions = {
    openOnFieldClick: false,
    calendarOptions: {
      zoomLevel: "century",
    },
  };

  const handleSave = (e: { preventDefault: () => void }) => {
    const { isValid } = (formRef.current as any)!.instance().validate();
    if (!isValid) {
      return;
    }
    household.id = 1;
    onSave({ ...household }, false);
    // onClose();
  };

  const handleRemoveMaidenName = (index: number) => {
    const newMaidenNames = maidenNames.filter((_, i) => i !== index);
    setMaidenNames(newMaidenNames);
  };

  const colCountByScreen = {
    xs: 2,
    sm: 2,
    md: 2,
  };

  return (
    <form onSubmit={handleSave} style={{ height: "100%" }}>
      <Form
        // @ts-ignore
        stylingMode="outlined"
        labelMode="floating"
        ref={formRef}
        formData={household}
        showValidationSummary={true}
        colCountByScreen={colCountByScreen}
        screenByWidth={screenByWidth}
      >
        {!isPersonal && (
          <SimpleItem
            dataField="relationship"
            editorType="dxSelectBox"
            editorOptions={countryEditorOptions}
            colSpan={2}
          >
            <RequiredRule message="Relationship is required" />
          </SimpleItem>
        )}
        <Item dataField="lastName">
          <RequiredRule message="Last Name is required" />
        </Item>
        <Item dataField="firstName">
          <RequiredRule message="First Name is required" />
        </Item>
        <Item
          dataField="gender"
          editorType="dxSelectBox"
          editorOptions={genderOptions}
        >
          <RequiredRule message="Gender is required" />
        </Item>
        <Item
          dataField="dateOfBirth"
          editorType="dxDateBox"
          editorOptions={birthDateOptions}
        >
          <RequiredRule message="Date Of Birth is required" />
        </Item>
      </Form>

      <Box>
        <Typography variant="h6" component="div">
          Maiden/alias/married Name (if applicable)
        </Typography>
        <Typography variant="caption">
          Any maiden names, previous married names, or aliases by which the
          applicant is or has been known.
        </Typography>
      </Box>

      <Stack spacing={2} mt={maidenNames.length > 0 ? 2 : 0}>
        {maidenNames.map((_, index) => (
          <Box key={index} sx={{ display: "flex", alignItems: "strech" }}>
            <TextBox
              style={{ flex: 1 }}
              value={maidenNames[index]}
              onValueChanged={(e: any) => {
                const newMaidenNames = [...maidenNames];
                newMaidenNames[index] = e.value;
                setMaidenNames(newMaidenNames);
              }}
            />
            <IconButton
              size="small"
              color="info"
              onClick={() => handleRemoveMaidenName(index)}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        ))}
      </Stack>

      <Button
        variant="text"
        color="primary"
        onClick={() => setMaidenNames([...maidenNames, ""])}
      >
        Add Maiden Name
      </Button>

      <Box mt={1} sx={{ display: "flex", justifyContent: "flex-end" }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
        >
          Save
        </Button>
      </Box>
    </form>
  );
};

function screenByWidth(width: number) {
  return width < 720 ? "sm" : "md";
}

export default HouseholdMemberForm;
