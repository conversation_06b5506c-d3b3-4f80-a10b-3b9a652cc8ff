"use client";
import { Container } from "@mui/material";
import React from "react";
import { useMediaQuery, useTheme } from "@mui/material";
import "devextreme/dist/css/dx.light.css";
import RootLayout from "@/app/layout";

const Layout = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <RootLayout>
      <Container maxWidth="md" sx={{ padding: isMobile ? "0" : "16px" }}>
        {children}
      </Container>
    </RootLayout>
  );
};

export default Layout;
