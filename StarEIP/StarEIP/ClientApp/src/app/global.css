.buttons-group {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.buttons-group .dx-item-content {
  gap: 10px;
}

.pac-container {
  background-color: #fff;
  z-index: 20000;
  position: fixed;
  display: inline-block;
  float: left;
}

/* Add this to your CSS file or within a <style> tag in your component */
.dx-layout-manager .dx-field-item:not(.dx-last-col) {
  padding-inline-end: 0px !important;
}

.button-column a.dx-link {
  margin-right: 8px !important;
}

.dx-datagrid {
  background-color: var(--mantine-color-body);
  color: var(--mantine-color-text);
}

.dx-datagrid-headers {
  color: var(--mantine-color-dimmed);
}

.parent-hover-tracker:hover .hidden-child {
  visibility: visible;
  opacity: 1;
}

.hidden-child {
  visibility: hidden;
  opacity: 0;
  transition:
    visibility 0s,
    opacity 0.5s linear;
}

.upload-report-icon {
  background-image: url("/UploadReport.png"); /* Ensure the path is correct */
  background-size: cover !important;
  width: 32px; /* Adjust as needed */
  height: 32px; /* Adjust as needed */
}

.dx-header-filter-empty {
  display: none !important;
}

.dx-datagrid-action:hover .dx-header-filter-empty {
  display: inline-block !important;
}
