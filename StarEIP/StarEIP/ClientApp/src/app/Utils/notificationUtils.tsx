import { notifications } from "@mantine/notifications";
import { IconCheck, IconX } from "@tabler/icons-react";

type NotificationType = "success" | "error" | "info" | "warning";

interface NotificationOptions {
  title?: string;
  message: string;
  icon?: React.ReactNode;
  color?: string;
  autoClose?: number | boolean;
}

/**
 * Show a notification with predefined styles based on type
 * @param type The type of notification: 'success', 'error', 'info', or 'warning'
 * @param message The message to display
 * @param options Additional options to customize the notification
 */
export const showNotification = (
  type: NotificationType,
  message: string,
  options: Partial<NotificationOptions> = {},
) => {
  const defaults: Record<NotificationType, NotificationOptions> = {
    success: {
      title: "Success",
      message,
      icon: <IconCheck size={16} />,
      color: "teal",
      autoClose: 3000,
    },
    error: {
      title: "Error",
      message,
      icon: <IconX size={16} />,
      color: "red",
      autoClose: 3000,
    },
    info: {
      title: "Information",
      message,
      color: "blue",
      autoClose: 3000,
    },
    warning: {
      title: "Warning",
      message,
      color: "yellow",
      autoClose: 3000,
    },
  };

  notifications.show({
    ...defaults[type],
    ...options,
  });
};
