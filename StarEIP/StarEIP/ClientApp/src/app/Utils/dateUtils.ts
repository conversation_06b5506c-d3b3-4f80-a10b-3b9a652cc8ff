// src/utils/dateUtils.ts

/**
 * Calculate age from date of birth
 * @param dateOfBirth - Date of birth string or Date object
 * @returns Age as a string with one decimal place
 */
export const calculateAge = (dateOfBirth: string | Date): string => {
  try {
    const dob = new Date(dateOfBirth);
    const today = new Date();

    let age = today.getFullYear() - dob.getFullYear();
    let monthDiff = today.getMonth() - dob.getMonth();
    let dayDiff = today.getDate() - dob.getDate();

    // Adjust for negative month difference (if the birth month hasn't occurred yet this year)
    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      age--;
      monthDiff += 12;
    }

    // Adjust the months into a decimal fraction
    const decimalAge = age + monthDiff / 12;

    return decimalAge.toFixed(1); // Return age with one decimal place
  } catch (e) {
    return (e as Error).message;
  }
};

/**
 * Format a date string or Date object to a more readable format
 * @param date - Date string, Date object, or undefined
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string | undefined): string => {
  if (!date) return "N/A";

  let dateObj: Date;

  if (typeof date === "string") {
    // If it's a string, parse it first
    dateObj = new Date(date);
  } else if (date instanceof Date) {
    dateObj = date;
  } else {
    return "Invalid Date";
  }

  // Check if date is valid
  if (isNaN(dateObj.getTime())) return "Invalid date";

  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  }).format(dateObj);
};

export default calculateAge;
