.main-container {
  border-radius: 30px !important;
}

.headerBox {
  background-color: #004b8b;
  border-radius: 30px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}

@media (max-width: 600px) {
  .main-container {
    border-radius: 0px !important;
  }

  .headerBox {
    border-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.buttons-group {
  display: flex;
  width: 100%;
  justify-content: end;
}

.buttons-group .dx-item-content {
  gap: 10px;
}
