"use client";

import { useEffect } from "react";
import config from "devextreme/core/config";
import dxTextBox from "devextreme/ui/text_box";
import dxDateBox from "devextreme/ui/date_box";
import dxSelectBox from "devextreme/ui/select_box";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import dxTextArea from "devextreme/ui/text_area";
import { inter } from "../fonts";
import { ColorSchemeScript, MantineProvider } from "@mantine/core";
import axios from "axios";
import { useRouter } from "next/navigation";
import urlHelpers from "../urlHelpers";
import { Notifications } from "@mantine/notifications";

const theme = createTheme({
  palette: {
    success: {
      main: "#00C100",
    },
  },
  typography: {
    h3: {
      fontFamily: inter.style.fontFamily,
      fontWeight: 500,
      fontSize: "20px",
      lineHeight: "24.2px",
      color: "#000000",
    },
    h6: {
      fontFamily: inter.style.fontFamily,
      fontWeight: 500,
      fontSize: "14px",
      lineHeight: "16.94px",
      color: "#000000",
    },
    caption: {
      fontFamily: inter.style.fontFamily,
      fontWeight: 400,
      fontSize: "12px",
      lineHeight: "14.52px",
      color: "#808080",
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        text: {
          textTransform: "none",
          margin: "6px 0px",
          padding: "0px",
          fontSize: "14px",
          fontWeight: "normal",
          textDecoration: "underline",
          "&:hover": {
            textDecoration: "underline",
            // If you want to change the hover background color, you can add it here
            // backgroundColor: 'transparent' // Uncomment this if you want no background on hover
          },
        },
      },
    },
  },
});

const DevExtremeSetup = ({ children, useMantine }) => {
  const router = useRouter();
  config({
    licenseKey:
      "ewogICJmb3JtYXQiOiAxLAogICJjdXN0b21lcklkIjogIjQ5ZDQ3Y2E0LWQ0NDMtMTFlMy1iZWIzLWRjODVkZTQwMDIyZSIsCiAgIm1heFZlcnNpb25BbGxvd2VkIjogMjQxCn0=.bz19LzR8eUT4xKqcl/k1mAGwwi8KGELixvyIsHEwpmS7T+9Ql8SR8SWdf2sGyJce4lVrmD+5XgpvTzIHnKF4pAToS55YOLi1mL2wQdQcuU5U5Vd9iQ+ZhFqTD/IfhpUA/oYGOw==",
  });

  axios.interceptors.request.use((request) => {
    request.baseURL = urlHelpers.getAbsoluteURL();
    const token = localStorage.getItem("jwtToken");
    if (token) {
      request.headers.Authorization = `Bearer ${token}`;
    }
    return request;
  });

  axios.interceptors.response.use(
    function (response) {
      return response;
    },
    function (error) {
      if (error.response?.status === 401) {
        router.push(`/login?returnUrl=${router.pathname}`);
      }
      return Promise.reject(error);
    },
  );

  if (useMantine) {
    return (
      <MantineProvider
        theme={{
          headings: {
            fontFamily: inter.style.fontFamily,
            sizes: {
              h3: {
                fontFamily: inter.style.fontFamily,
                fontWeight: 500,
                fontSize: 20,
                lineHeight: "16.94px",
                color: "#000000",
              },
              h6: {
                fontFamily: inter.style.fontFamily,
                fontWeight: 500,
                fontSize: 14,
                lineHeight: "16.94px",
                color: "#000000",
              },
            },
          },
          primaryColor: "teal",
        }}
      >
        <Notifications />
        {children}
      </MantineProvider>
    );
  }

  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <ThemeProvider theme={theme}>{children}</ThemeProvider>
      </LocalizationProvider>
    </>
  );
};

export default DevExtremeSetup;

// dxTextBox.defaultOptions({
//     options: {
//         stylingMode: 'outlined',
//         labelMode: 'floating'
//     }
// });

// dxDateBox.defaultOptions({
//     options: {
//         stylingMode: 'outlined',
//         labelMode: 'floating',
//         format: 'MM/DD/YYYY'
//     }
// });

// dxSelectBox.defaultOptions({
//     options: {
//         stylingMode: 'outlined',
//         labelMode: 'floating'
//     }
// });

// dxTextArea.defaultOptions({
//     options: {
//         stylingMode: 'outlined',
//         labelMode: 'floating'
//     }
// });
