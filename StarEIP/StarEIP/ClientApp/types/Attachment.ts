import React from "react";

export interface Attachment {
  id: number;
  fileName: string;
  filePath: string;
  fileType: string;
  description: string;
  createdAt: string;
  createdBy: string;
  shareCount: number;
}

export interface Contact {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  faxNumber: string;
}

export interface ShareLog {
  id: number;
  sharedOn: string;
  sharedBy: string;
  sharedMethod: string;
}

export interface PreviewConfig {
  containerType: string;
  baseUrl: string;
}

export interface AttachmentsProps {
  attachments: Attachment[];
  entityType: string;
  entityId: number | string;

  customToolbarItems?: React.ReactNode;
  previewConfig?: PreviewConfig;
  fetchAttachments: () => void;
}
