﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Net.Http.Headers;
using StarEIP.Models;
using StarEIP.Models.App;
using StarEIP.Models.Auth;
using StarEIP.Models.Common;
using StarEIP.Models.Import;
using StarEIP.Models.SCR;
using StarEIP.Models.Tasks;

namespace StarEIP
{
    public class StarEipDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, int>
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        public StarEipDbContext(DbContextOptions<StarEipDbContext> options, IHttpContextAccessor httpContextAccessor) :
            base(options)
        {
            this.httpContextAccessor = httpContextAccessor;
        }

        public DbSet<Child> Children { get; set; }
        public DbSet<Notes> Notes { get; set; }
        public DbSet<ChildStatus> ChildStatuses { get; set; }
        public DbSet<ChildAttachment> ChildAttachments { get; set; }
        public DbSet<Physician> Physicians { get; set; }
        public DbSet<Models.Authorization> Authorizations { get; set; }
        public DbSet<AuthorizationStatus> AuthorizationStatuses { get; set; }
        public DbSet<ScStatusType> ScStatusTypes { get; set; }
        public DbSet<Staff> Staff { get; set; }
        public DbSet<ScrForm> ScrForms { get; set; }
        public DbSet<ScrHouseholdMember> ScrHouseholdMembers { get; set; }
        public DbSet<ScrAddressHistory> ScrAddressHistories { get; set; }
        public DbSet<ScrFormLink> ScrFormLinks { get; set; }
        public DbSet<ScrFormEvent> ScrFormEvents { get; set; }
        public DbSet<ScrFormInfo> ScrFormInfos { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<EmailTemplate> EmailTemplates { get; set; }
        public DbSet<Fax> Faxes { get; set; }
        public DbSet<FaxChild> FaxChildren { get; set; }
        public DbSet<AuthLog> AuthLogs { get; set; }
        public DbSet<ReportItem> Reports { get; set; }
        public DbSet<SharedAttachmentLog> SharedAttachmentLogs { get; set; }
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<SmsMessage> Messages { get; set; }
        public DbSet<ImportReconciliationEntry> ImportReconciliationEntries { get; set; }
        public DbSet<ImportChildInfoAuthorization> ImportChildInfoAuthorizations { get; set; }
        public DbSet<ImportChildDetailReport> ImportChildDetailReports { get; set; }
        public DbSet<ImportPsAllChildren> ImportPsAllChildren { get; set; }
        public DbSet<ImportChildLookupReport> ImportChildLookupReports { get; set; }
        public DbSet<ImportType> ImportTypes { get; set; }
        public DbSet<ImportLog> ImportLogs { get; set; }

        public DbSet<TaskItem> TaskItems { get; set; }
        public DbSet<TaskTemplate> TaskTemplates { get; set; }
        public DbSet<Models.Tasks.TaskStatus> TaskStatuses { get; set; }
        public DbSet<TaskItemLink> TaskItemLinks { get; set; }
        public DbSet<EmailMessage> EmailMessages { get; set; }
        public DbSet<TaskItemEmail> TaskItemEmails { get; set; }
        public DbSet<ChildReconciliation> ChildReconciliations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Child>()
               .Property(p => p.PatientFullName)
               .HasComputedColumnSql("FirstName + ' ' + LastName");

            modelBuilder.Entity<ScrFormInfo>().HasNoKey().ToView("ScrFormInfo");
            modelBuilder.Entity<ChildReconciliation>().HasNoKey().ToView("vw_ChildReconciliation");

            modelBuilder.Entity<SmsMessage>()
                .HasIndex(m => m.ExternalMessageId);

            modelBuilder.Entity<SmsMessage>()
                .HasIndex(m => m.ChildId);

            modelBuilder.Entity<FaxChild>()
                .HasOne<Fax>()
                .WithMany()
                .HasForeignKey(fc => fc.FaxId);

            modelBuilder.Entity<FaxChild>()
               .HasOne<Child>()
               .WithMany()
               .HasForeignKey(fc => fc.ChildId);

            modelBuilder.Entity<ImportReconciliationEntry>()
                .ToTable("Import_ReconciliationEntry")
                .HasIndex(e => new { e.EiNumber, e.Provider, e.SessionDate, e.Type, e.SubType })
                .IsUnique();

            modelBuilder.Entity<ImportChildInfoAuthorization>()
                .ToTable("Import_ChildInfoAuthorization")
                .HasIndex(e => new { e.EiChildId, e.AuthorizationNumber })
                .IsUnique();

            modelBuilder.Entity<ImportChildDetailReport>()
                .ToTable("Import_ChildDetailReport")
                .HasIndex(e => e.EiChildId);

            modelBuilder.Entity<ImportPsAllChildren>()
                .ToTable("Import_PsAllChildren")
                .HasIndex(e => e.ProgramId);

            modelBuilder.Entity<ImportChildLookupReport>()
                .ToTable("Import_ChildLookupReport")
                .HasIndex(e => e.EiChildId);

            modelBuilder.Entity<ImportLog>()
                .HasOne(log => log.ImportType)
                .WithMany()
                .HasForeignKey(log => log.ImportTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ImportLog>()
                .HasOne(log => log.ImportedByUser)
                .WithMany()
                .HasForeignKey(log => log.ImportedByUserId)
                .OnDelete(DeleteBehavior.Restrict);


            modelBuilder.Entity<TaskItem>()
                .HasOne(t => t.TaskTemplate)
                .WithMany(t => t.TaskItems)
                .HasForeignKey(t => t.TaskTemplateId);

            modelBuilder.Entity<TaskItem>()
                .HasOne(t => t.Status)
                .WithMany(s => s.TaskItems)
                .HasForeignKey(t => t.TaskStatusId);

            modelBuilder.Entity<TaskItem>()
                .HasOne(t => t.AssignedToUser)
                .WithMany()
                .HasForeignKey(t => t.AssignedToUserId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<TaskItem>()
                .HasOne(t => t.Child)
                .WithMany()
                .HasForeignKey(t => t.ChildId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<TaskItemLink>()
                .HasIndex(t => new { t.LinkTable, t.LinkId })
                .IsUnique();

            // Set default string length to 255
            foreach (var entity in modelBuilder.Model.GetEntityTypes())
            {
                if (entity == null || entity.ClrType?.Namespace?.StartsWith("Microsoft.AspNetCore.Identity") == true)
                {
                    continue;
                }

                foreach (var property in entity.GetProperties())
                {
                    if (property.ClrType == typeof(string) && !property.GetAnnotations().Any())
                    {
                        property.SetMaxLength(255);
                    }
                }
            }
        }

        public override int SaveChanges()
        {
            var auditEntries = OnBeforeSaveChanges();
            var result = base.SaveChanges();
            OnAfterSaveChanges(auditEntries);
            return result;
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var auditEntries = OnBeforeSaveChanges();
            var result = await base.SaveChangesAsync();
            OnAfterSaveChanges(auditEntries);
            return result;
        }

        private List<AuditEntry> OnBeforeSaveChanges()
        {
            ChangeTracker.DetectChanges();
            var auditEntries = new List<AuditEntry>();
            int? userId = null;
            Guid? jwtGuid = null;
            var ipAddress = httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
            var userAgent = httpContextAccessor.HttpContext?.Request.Headers[HeaderNames.UserAgent].ToString();
            if (httpContextAccessor.HttpContext?.User.Identity?.IsAuthenticated == true)
            {
                userId = httpContextAccessor.HttpContext.User.GetUserId();
                jwtGuid = httpContextAccessor.HttpContext.User.GetJwtGuid();
            }

            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.Entity is AuditLog || entry.State == EntityState.Detached || entry.State == EntityState.Unchanged)
                {
                    continue;
                }

                if (entry.Entity is BaseEntity baseEntity)
                {
                    if (entry.State == EntityState.Added)
                    {
                        baseEntity.CreatedBy = userId;
                        baseEntity.CreatedOn = DateTime.Now;
                    }
                    else if (entry.State == EntityState.Modified)
                    {
                        baseEntity.UpdatedBy = userId;
                        baseEntity.UpdatedOn = DateTime.Now;
                    }
                }

                var auditEntry = new AuditEntry(entry)
                {
                    TableName = entry.Entity.GetType().Name,
                    UserId = userId,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    JwtGuid = jwtGuid
                };

                foreach (var property in entry.Properties)
                {
                    string propertyName = property.Metadata.Name;
                    if (property.IsTemporary)
                    {
                        auditEntry.TemporaryProperties.Add(property);
                        continue;
                    }

                    string? oldValue = property.OriginalValue?.ToString();
                    string? newValue = property.CurrentValue?.ToString();

                    if (oldValue != newValue)
                    {
                        auditEntry.Changes.Add(new AuditChange
                        {
                            ColumnName = propertyName,
                            OldValue = oldValue,
                            NewValue = newValue
                        });
                    }
                }

                auditEntries.Add(auditEntry);
            }

            return auditEntries;
        }

        private void OnAfterSaveChanges(List<AuditEntry> auditEntries)
        {
            if (auditEntries == null || auditEntries.Count == 0)
            {
                return;
            }

            foreach (var auditEntry in auditEntries)
            {
                foreach (var change in auditEntry.Changes)
                {
                    var auditLog = new AuditLog
                    {
                        UserId = auditEntry.UserId,
                        IpAddress = auditEntry.IpAddress,
                        TableName = auditEntry.TableName,
                        PrimaryKey = auditEntry.PrimaryKey,
                        PrimaryKeyString = auditEntry.PrimaryKeyString,
                        ColumnName = change.ColumnName,
                        OldValue = change.OldValue,
                        NewValue = change.NewValue,
                        Timestamp = DateTime.Now,
                        JwtGuid = auditEntry.JwtGuid,
                        UserAgent = auditEntry.UserAgent
                    };
                    AuditLogs.Add(auditLog);
                }
            }
            SaveChanges();
        }
    }
}
