﻿﻿using Flurl;
using Flurl.Http;
using Microsoft.Extensions.Options;
using StarEIP.Configuration;
using StarEIP.Models;
using StarEIP.Models.App.TelebroadApi;
using System.Text.Json;

namespace StarEIP.Services
{
    public class TelebroadApiService
    {
        private readonly TelebroadApiOptions _options;
        private readonly ILogger<TelebroadApiService> _logger;

        public TelebroadApiService(
            IOptions<TelebroadApiOptions> options,
            ILogger<TelebroadApiService> logger)
        {
            _options = options.Value;
            _logger = logger;
        }

        /// <summary>
        /// Sends a fax using the Telebroad API
        /// </summary>
        /// <param name="toNumber">The recipient's fax number</param>
        /// <param name="base64Data">The base64-encoded PDF data</param>
        /// <param name="filename">The filename of the PDF</param>
        /// <returns>A tuple with success status and message</returns>
        public async Task<(bool Success, string Message)> SendFaxAsync(string toNumber, string base64Data, string filename)
        {
            var response = await $"{_options.BaseUrl}/send/fax"
                .WithHeader("mailbox", _options.FaxNumber)
                .WithHeader("token", _options.Password)
                .WithBasicAuth(_options.Username, _options.Password)
                .PostJsonAsync(new
                {
                    to = toNumber,
                    data = base64Data,
                    filename = filename
                });

            var responseContent = await response.GetStringAsync();

            try
            {
                var result = JsonSerializer.Deserialize<TelebroadBaseResponse<object>>(responseContent);
                if (result.error == null)
                {
                    return (true, "Fax sent successfully");
                }
                else
                {
                    return (false, $"Failed to send fax: {result.error}");
                }
            }
            catch (JsonException)
            {
                // Fallback to the old string check if JSON parsing fails
                if (responseContent.Contains("\"error\":null") || responseContent.Contains("\"error\":\"\""))
                {
                    return (true, "Fax sent successfully");
                }
                else
                {
                    return (false, $"Failed to send fax: {responseContent}");
                }
            }
        }

        /// <summary>
        /// Gets fax messages from the Telebroad API
        /// </summary>
        /// <param name="offset">The offset for pagination</param>
        /// <param name="limit">The limit for pagination</param>
        /// <param name="startTime">The start time for filtering</param>
        /// <returns>A list of fax messages</returns>
        public async Task<TelebroadBaseResponse<List<TelebroadFaxMessages>>> GetFaxMessagesAsync(int offset, int limit, string? startTime = null)
        {
            var queryParams = new Dictionary<string, object>
            {
                { "descending", 1 },
                { "mailbox", _options.FaxNumber },
                { "limit", limit },
                { "offset", offset }
            };

            if (startTime != null)
            {
                queryParams.Add("start", startTime);
            }
            else
            {
                queryParams.Add("start", 0);
            }

            queryParams.Add("end", -1); // Get all faxes up to now

            var response = await $"{_options.BaseUrl}/fax/messages"
                .SetQueryParams(queryParams)
                .WithHeader("token", _options.Password)
                .WithBasicAuth(_options.Username, _options.Password)
                .GetStringAsync();

            return JsonSerializer.Deserialize<TelebroadBaseResponse<List<TelebroadFaxMessages>>>(response);
        }

        /// <summary>
        /// Gets a specific fax message from the Telebroad API
        /// </summary>
        /// <param name="fileName">The fax file name (from the 'name' property in the fax messages response)</param>
        /// <param name="direction">The direction of the fax (INBOX or SENT). Default is INBOX.</param>
        /// <returns>The fax message details</returns>
        public async Task<TelebroadBaseResponse<TelebroadFaxMessage>> GetFaxMessageAsync(Fax fax, string direction = "INBOX")
        {
            try
            {
                var url = $"{_options.BaseUrl}/fax/message";

                var response = await url
                    .SetQueryParam("mailbox", _options.FaxNumber)
                    .SetQueryParam("file", fax.FileName)
                    .SetQueryParam("dir", direction)
                    .WithBasicAuth(_options.Username, _options.Password)
                    .GetStringAsync();

                var result = JsonSerializer.Deserialize<TelebroadBaseResponse<TelebroadFaxMessage>>(response);

                if (result?.error != null)
                {
                    _logger.LogError("Error retrieving fax message {FileName}: {Error}", fax.FileName, result.error);
                    throw new Exception($"Error retrieving fax message {fax.FileName}: {result.error}");
                }
                return result;
            }
            catch (FlurlHttpException ex)
            {
                var errorDetails = await ex.GetResponseStringAsync();
                _logger.LogError(ex, "HTTP error retrieving fax message {FaxId}: {ErrorMessage}. Details: {ErrorDetails}",
                    fax.Id, ex.Message, errorDetails);
                throw new Exception($"HTTP error retrieving fax message {fax.Id}: {ex.Message}. Details: {errorDetails}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error retrieving fax message {FaxId}", fax.Id);
                throw;
            }
        }

        /// <summary>
        /// Sends an SMS using the Telebroad API
        /// </summary>
        /// <param name="fromNumber">The sender's phone number</param>
        /// <param name="toNumber">The recipient's phone number</param>
        /// <param name="message">The SMS message</param>
        /// <returns>The SMS response</returns>
        public async Task<TelebroadSmsResponse> SendSmsAsync(string fromNumber, string toNumber, string message)
        {
            var response = await $"{_options.BaseUrl}/send/sms"
                .WithHeader("token", _options.Password)
                .WithHeader("mailbox", _options.FaxNumber)
                .WithBasicAuth(_options.Username, _options.Password)
                .PostJsonAsync(new
                {
                    from = fromNumber.TrimStart('+'),
                    to = toNumber.TrimStart('+'),
                    text = message
                });

            var responseContent = await response.GetStringAsync();
            return JsonSerializer.Deserialize<TelebroadSmsResponse>(responseContent);
        }

        /// <summary>
        /// Gets SMS conversations from the Telebroad API
        /// </summary>
        /// <param name="offset">The offset for pagination</param>
        /// <param name="limit">The limit for pagination</param>
        /// <param name="startTime">The start time for filtering</param>
        /// <returns>A list of SMS conversations</returns>
        public async Task<TelebroadBaseResponse<List<TelebroadSmsMessage>>> GetSmsConversationsAsync(int limit, int? offset = null)
        {
            var queryParams = new Dictionary<string, object> { { "limit", limit } };
            if (offset.HasValue)
            {
                queryParams.Add("offset", offset.Value);
            }

            var response = await $"{_options.BaseUrl}/sms/conversations"
                .SetQueryParams(queryParams)
                .WithBasicAuth(_options.Username, _options.Password)
                .GetStringAsync();

            var parsed = JsonSerializer.Deserialize<TelebroadBaseResponse<List<TelebroadSmsMessage>>>(response);
            return parsed;
        }

        /// <summary>
        /// Gets a specific SMS conversation from the Telebroad API
        /// </summary>
        /// <param name="conversationId">The conversation ID</param>
        /// <returns>The SMS conversation details</returns>
        public async Task<TelebroadSmsConversationResponse> GetSmsConversationAsync(string conversationId)
        {
            var response = await $"{_options.BaseUrl}/sms/conversation"
                .SetQueryParam("id", conversationId)
                .WithHeader("token", _options.Password)
                .WithHeader("mailbox", _options.FaxNumber)
                .WithBasicAuth(_options.Username, _options.Password)
                .GetStringAsync();

            return JsonSerializer.Deserialize<TelebroadSmsConversationResponse>(response);
        }
    }
}
