using Microsoft.EntityFrameworkCore;
using StarEIP.DTOs;
using StarEIP.Models;

namespace StarEIP.Services.Data
{
    public class ChildDataService
    {
        private readonly StarEipDbContext _dbContext;

        public ChildDataService(StarEipDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public IQueryable<ChildDetailsDto> GetChildrenDto()
        {
            var children = from child in _dbContext.Children.Include(c => c.ReferringPhysician)
                           where child.DeletedOn == null
                           select new ChildDetailsDto
                           {
                               Id = child.Id,
                               FirstName = child.FirstName,
                               LastName = child.LastName,
                               PatientName = child.PatientFullName,
                               DateOfBirth = child.DateOfBirth,
                               Gender = child.Gender,
                               PrimaryLanguage = child.PrimaryLanguage,
                               ReasonForReferral = child.ReasonForReferral,
                               ParentName = child.ParentName,
                               ParentPhoneNumber = child.ParentPhoneNumber,
                               ParentEmail = child.ParentEmail,
                               FullAddress = child.FullAddress,
                               ReferringPhysicianName = child.ReferringPhysician.Name,
                               PhysicianPhoneNumber = child.ReferringPhysician.PhoneNumber,
                               PhysicianEmailAddress = child.ReferringPhysician.EmailAddress,
                               ReferralMethod = child.ReferralMethod,
                               ReferringPhysicianId = child.ReferringPhysicianId,
                               ProgramId = child.ProgramId,
                               ProviderSoftChildId = child.ProviderSoftChildId,
                               Status = child.Status,
                               StatusLastUpdated = child.StatusLastUpdated,
                               NotesCount = child.Notes.Count(),
                               AuthorizationsCount = child.Authorizations.Count(),
                               CreatedOn = child.CreatedOn,
                               UpdatedOn = child.UpdatedOn
                           };
            return children;
        }
    }
}