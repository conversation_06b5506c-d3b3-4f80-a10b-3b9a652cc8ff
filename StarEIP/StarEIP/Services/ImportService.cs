﻿﻿using AutoMapper;
using CsvHelper;
using CsvHelper.Configuration;
using DevExpress.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.AutoMapper;
using StarEIP.Models.Import;
using System.Globalization;

namespace StarEIP.Services;

public partial class ImportService(StarEipDbContext dbContext, IMapper mapper)
{
    private readonly StarEipDbContext _dbContext = dbContext;
    private readonly IMapper _mapper = mapper;

    public async Task ImportChildInfoAuthorizationsAsync(Stream excelStream)
    {
        using var csvStream = ConvertXlsxToCsvStream(excelStream);
        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            TrimOptions = TrimOptions.Trim,
            MissingFieldFound = null,
            HeaderValidated = null
        });

        var records = csv.GetRecords<ChildInfoAuthorizationCsv>().ToList();
        var now = DateTime.Now;

        foreach (var record in records)
        {
            var existing = await _dbContext.ImportChildInfoAuthorizations
                .FirstOrDefaultAsync(x => x.EiChildId == record.EIChildID && x.AuthorizationNumber == record.AuthorizationNumber);

            if (existing != null)
            {
                _mapper.Map(record, existing);
                if (_dbContext.Entry(existing).State == EntityState.Modified)
                {
                    existing.UpdatedAt = now;
                }
                existing.LastImportedAt = now;
            }
            else
            {
                var newEntry = _mapper.Map<ImportChildInfoAuthorization>(record);
                newEntry.CreatedAt = now;
                newEntry.UpdatedAt = now;
                newEntry.LastImportedAt = now;

                _dbContext.ImportChildInfoAuthorizations.Add(newEntry);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task ImportReconciliationAsync(Stream csvStream)
    {
        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            TrimOptions = TrimOptions.Trim,
            MissingFieldFound = null,
            HeaderValidated = null
        });

        var records = csv.GetRecords<ReconciliationEntryCsv>().ToList();
        var now = DateTime.UtcNow;
        var seenKeys = new HashSet<string>();

        foreach (var csvEntry in records)
        {
            var key = $"{csvEntry.EiNumber}|{csvEntry.Provider}|{csvEntry.SessionDate:yyyy-MM-dd}|{csvEntry.Type}|{csvEntry.SubType}";

            if (!seenKeys.Add(key)) continue;

            var existing = await _dbContext.ImportReconciliationEntries.FirstOrDefaultAsync(e =>
                e.EiNumber == csvEntry.EiNumber &&
                e.Provider == csvEntry.Provider &&
                e.SessionDate == csvEntry.SessionDate &&
                e.Type == csvEntry.Type &&
                e.SubType == csvEntry.SubType);

            if (existing != null)
            {
                _mapper.Map(csvEntry, existing);
                if (_dbContext.Entry(existing).State == EntityState.Modified)
                {
                    existing.UpdatedAt = now;
                }
                existing.LastImportedAt = now;
            }
            else
            {
                var newEntry = _mapper.Map<ImportReconciliationEntry>(csvEntry);
                newEntry.CreatedAt = now;
                newEntry.UpdatedAt = now;
                newEntry.LastImportedAt = now;
                _dbContext.ImportReconciliationEntries.Add(newEntry);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task ImportChildDetailReportAsync(Stream excelStream)
    {
        using var csvStream = ConvertXlsxToCsvStream(excelStream);
        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            TrimOptions = TrimOptions.Trim
        });

        var records = csv.GetRecords<ChildDetailReportCsv>().ToList();
        var now = DateTime.Now;

        foreach (var record in records)
        {
            var existing = await _dbContext.ImportChildDetailReports
                .FirstOrDefaultAsync(x => x.EiChildId == record.EiChildId);

            if (existing != null)
            {
                _mapper.Map(record, existing);
                if (_dbContext.Entry(existing).State == EntityState.Modified)
                {
                    existing.UpdatedAt = now;
                }
                existing.LastImportedAt = now;
            }
            else
            {
                var newEntry = _mapper.Map<ImportChildDetailReport>(record);
                newEntry.CreatedAt = now;
                newEntry.UpdatedAt = now;
                newEntry.LastImportedAt = now;

                _dbContext.ImportChildDetailReports.Add(newEntry);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task ImportPsAllChildrenAsync(Stream csvStream)
    {
        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            TrimOptions = TrimOptions.Trim,
            MissingFieldFound = null,
            HeaderValidated = null
        });

        var records = csv.GetRecords<PsAllChildrenCsv>().ToList();
        var now = DateTime.Now;

        foreach (var record in records)
        {
            // Use ProgramId as the unique identifier for matching existing records
            var existing = await _dbContext.ImportPsAllChildren
                .FirstOrDefaultAsync(x => x.ProgramId == record.ProgramId && !string.IsNullOrEmpty(x.ProgramId));

            if (existing != null)
            {
                _mapper.Map(record, existing);
                if (_dbContext.Entry(existing).State == EntityState.Modified)
                {
                    existing.UpdatedAt = now;
                }
                existing.LastImportedAt = now;
            }
            else
            {
                var newEntry = _mapper.Map<ImportPsAllChildren>(record);
                newEntry.CreatedAt = now;
                newEntry.UpdatedAt = now;
                newEntry.LastImportedAt = now;

                _dbContext.ImportPsAllChildren.Add(newEntry);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task ImportChildLookupReportAsync(Stream excelStream)
    {
        using var csvStream = ConvertXlsxToCsvStream(excelStream);
        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            TrimOptions = TrimOptions.Trim
        });

        var records = csv.GetRecords<ChildLookupReportCsv>().ToList();
        var now = DateTime.Now;

        foreach (var record in records)
        {
            var existing = await _dbContext.ImportChildLookupReports
                .FirstOrDefaultAsync(x => x.EiChildId == record.EiChildId);

            if (existing != null)
            {
                _mapper.Map(record, existing);
                if (_dbContext.Entry(existing).State == EntityState.Modified)
                {
                    existing.UpdatedAt = now;
                }
                existing.LastImportedAt = now;
            }
            else
            {
                var newEntry = _mapper.Map<ImportChildLookupReport>(record);
                newEntry.CreatedAt = now;
                newEntry.UpdatedAt = now;
                newEntry.LastImportedAt = now;

                _dbContext.ImportChildLookupReports.Add(newEntry);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    private static Stream ConvertXlsxToCsvStream(Stream inputXlsxStream)
    {
        inputXlsxStream.Position = 0;
        var workbook = new Workbook();
        workbook.LoadDocument(inputXlsxStream, DocumentFormat.OpenXml);
        var sheet = workbook.Worksheets[0];
        if (sheet.GetUsedRange().RowCount == 0)
        {
            throw new InvalidOperationException("The Excel sheet is empty or unreadable.");
        }
        var outputCsvStream = new MemoryStream();
        workbook.SaveDocument(outputCsvStream, DocumentFormat.Csv);
        workbook.Dispose();

        outputCsvStream.Position = 0;
        return outputCsvStream;
    }
}