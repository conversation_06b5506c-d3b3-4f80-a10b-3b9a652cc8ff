using Microsoft.EntityFrameworkCore;
using StarEIP.Models;

namespace StarEIP.Services
{
    public class SmsBackgroundService : BackgroundService
    {
        private readonly ILogger<SmsBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public SmsBackgroundService(
            ILogger<SmsBackgroundService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("SmsBackgroundService running at: {time}", DateTimeOffset.Now);
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<StarEipDbContext>();
            var telebroadApiService = scope.ServiceProvider.GetRequiredService<TelebroadApiService>();

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var conversationsResponse = await telebroadApiService.GetSmsConversationsAsync(250);
                    if (conversationsResponse.error != null)
                    {
                        throw new Exception($"Error retrieving SMS conversations: {conversationsResponse.error}");
                    }

                    var conversations = conversationsResponse.result;
                    if (conversations == null || conversations.Count == 0)
                        break;

                    foreach (var conversation in conversations)
                    {
                        if (!await dbContext.Messages.AnyAsync(m => m.ExternalMessageId == conversation.Time.ToString(), stoppingToken))
                        {
                            var newMessage = new SmsMessage
                            {
                                ExternalMessageId = conversation.Time.ToString(),
                                From = FormatPhoneNumber(conversation.From),
                                To = FormatPhoneNumber(conversation.To),
                                Text = conversation.Msgdata,
                                CreatedAt = DateTimeOffset.FromUnixTimeSeconds(conversation.Time).DateTime,
                                Direction = conversation.Direction,
                            };
                            await dbContext.Messages.AddAsync(newMessage, stoppingToken);
                        }
                    }
                    await dbContext.SaveChangesAsync(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing SMS conversations batch");
                    break;
                }
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }

        private static string FormatPhoneNumber(string phoneNumber)
        {
            return phoneNumber.StartsWith($"+") ? phoneNumber : $"+1{phoneNumber}";
        }
    }
}