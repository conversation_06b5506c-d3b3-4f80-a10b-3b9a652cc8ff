using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using StarEIP.Models.Tasks;
using StarEIP.Services.Core;

namespace StarEIP.Services
{
    public class EmailTaskSyncService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<EmailTaskSyncService> _logger;

        public EmailTaskSyncService(IServiceProvider serviceProvider, ILogger<EmailTaskSyncService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("EmailTaskSyncService started.");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var dbContext = scope.ServiceProvider.GetRequiredService<StarEipDbContext>();
                        var emailService = scope.ServiceProvider.GetRequiredService<EmailService>();

                        _logger.LogInformation("Fetching the last 10 emails...");
                        var emails = await emailService.GetLastTenEmailsAsync("<EMAIL>");

                        if (emails == null || !emails.Any())
                        {
                            _logger.LogInformation("No new emails found.");
                        }
                        else
                        {
                            foreach (var email in emails)
                            {
                                await ProcessEmailAsync(email, dbContext, stoppingToken);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "An error occurred while syncing emails.");
                }
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
            _logger.LogInformation("EmailTaskSyncService stopped.");
        }

        private async Task ProcessEmailAsync(EmailService.EmailSummary email, StarEipDbContext dbContext, CancellationToken stoppingToken)
        {
            try
            {
                // Check if the email already exists in the database
                var emailMessage = await dbContext.EmailMessages.FirstOrDefaultAsync(e => e.GraphMessageId == email.Id, stoppingToken);

                if (emailMessage == null && email.Id != null)
                {
                    emailMessage = await CreateEmailMessageAsync(email, dbContext, stoppingToken);
                }

                // Check if the email is already linked to a task
                bool isLinked = await dbContext.TaskItemEmails.AnyAsync(e => e.EmailMessageId == emailMessage.Id, stoppingToken);

                if (!isLinked)
                {
                    await CreateTaskForEmailAsync(email, emailMessage, dbContext, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing email with ID: {EmailId}", email.Id);
            }
        }

        private async Task<EmailMessage> CreateEmailMessageAsync(EmailService.EmailSummary email, StarEipDbContext dbContext, CancellationToken stoppingToken)
        {
            var emailMessage = new EmailMessage
            {
                GraphMessageId = email.Id,
                Subject = email.Subject ?? "No Subject",
                SenderEmail = email.From?.EmailAddress?.Address,
                ReceivedAt = email.ReceivedDateTime?.UtcDateTime ?? DateTime.UtcNow,
                WebLink = string.Empty, // Placeholder for WebLink
                InternetMessageId = string.Empty // Placeholder for InternetMessageId
            };

            dbContext.EmailMessages.Add(emailMessage);
            await dbContext.SaveChangesAsync(stoppingToken);

            _logger.LogInformation("EmailMessage created with ID: {EmailMessageId}", emailMessage.Id);
            return emailMessage;
        }

        private async Task CreateTaskForEmailAsync(EmailService.EmailSummary email, EmailMessage emailMessage, StarEipDbContext dbContext, CancellationToken stoppingToken)
        {
            // Create a new TaskItem
            var taskItem = new TaskItem
            {
                Title = email.Subject ?? "New Email Task",
                CreatedAt = DateTime.UtcNow,
                DueAt = email.ReceivedDateTime?.UtcDateTime.AddDays(7) ?? DateTime.UtcNow.AddDays(7),
                TaskTemplateId = 1, // Replace with a valid TaskTemplateId
                TaskStatusId = 1    // Replace with a valid TaskStatusId
            };

            dbContext.TaskItems.Add(taskItem);
            await dbContext.SaveChangesAsync(stoppingToken);

            // Link the email to the task
            var taskItemEmail = new TaskItemEmail
            {
                TaskItemId = taskItem.Id,
                EmailMessageId = emailMessage.Id,
                LinkType = EmailLinkType.CreatedTask,
                CreatedAt = DateTime.UtcNow
            };

            dbContext.TaskItemEmails.Add(taskItemEmail);
            await dbContext.SaveChangesAsync(stoppingToken);

            _logger.LogInformation("TaskItem created and linked to EmailMessage with ID: {EmailMessageId}", emailMessage.Id);
        }
    }
}