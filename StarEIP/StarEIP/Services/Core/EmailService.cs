﻿using Azure.Identity;
using Microsoft.Graph.Models;
using Microsoft.Graph;
using Microsoft.AspNetCore.DataProtection;
using StarEIP.Models;
using Microsoft.EntityFrameworkCore;
using Fluid;
using Parlot.Fluent;

namespace StarEIP.Services.Core
{
    public class EmailService
    {
        private readonly StarEipDbContext dbContext;
        private readonly FluidParser fluidParser;

        public EmailService(StarEipDbContext dbContext, FluidParser fluidParser)
        {
            this.dbContext = dbContext;
            this.fluidParser = fluidParser;
        }

        public async Task SendAsync(string fromAddress, string toAddress, string subject, string content)
        {
            string? tenantId = "eda89739-547c-4048-9dd4-f2bb6f90bf43";
            string? clientId = "614c03d3-a424-4a35-89e4-2b13e4e12ec8";
            string? clientSecret = "****************************************";
            // secret id: e08f7346 - c371 - 404c - 9507 - 489abb5358fe
            //value: ****************************************

            ClientSecretCredential credential = new(tenantId, clientId, clientSecret);
            GraphServiceClient graphClient = new(credential);

            Message message = new()
            {
                Subject = subject,
                Body = new ItemBody
                {
                    ContentType = BodyType.Html,
                    Content = content
                },
                ToRecipients =
                [
                    new()
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = toAddress
                        }
                    }
                ],
                //From = new Recipient{EmailAddress = new EmailAddress{Address = "<EMAIL>"}}
            };

            bool saveToSentItems = true;

            await graphClient.Users[fromAddress].SendMail.PostAsync(
                new Microsoft.Graph.Users.Item.SendMail.SendMailPostRequestBody
                {
                    Message = message,
                    SaveToSentItems = saveToSentItems
                });
        }

        public async Task<List<EmailSummary>?> GetLastTenEmailsAsync(string userAddress, string? filterEmail = null)
        {
            string tenantId = "eda89739-547c-4048-9dd4-f2bb6f90bf43";
            string clientId = "614c03d3-a424-4a35-89e4-2b13e4e12ec8";
            string clientSecret = "****************************************";

            ClientSecretCredential credential = new(tenantId, clientId, clientSecret);
            GraphServiceClient graphClient = new(credential);

            var messages = await graphClient.Users[userAddress]
                .MailFolders["Inbox"]
                .Messages
                .GetAsync(requestConfig =>
                {
                    requestConfig.QueryParameters.Top = 10;
                    requestConfig.QueryParameters.Orderby = new[] { "receivedDateTime desc" };
                    requestConfig.QueryParameters.Select = new[]
                    {
                        "id", "subject", "from", "toRecipients", "ccRecipients", "receivedDateTime", "hasAttachments",
                        "bodyPreview"
                    };
                });

            return messages?.Value?.Select(m => new EmailSummary
            {
                Id = m.Id,
                Subject = m.Subject,
                From = m.From,
                ToRecipients = m.ToRecipients,
                CcRecipients = m.CcRecipients,
                ReceivedDateTime = m.ReceivedDateTime,
                HasAttachments = m.HasAttachments,
                BodyPreview = m.BodyPreview
            }).ToList();
        }

        public async Task<string?> GetEmailBodyAsync(string userAddress, string emailId)
        {
            string tenantId = "eda89739-547c-4048-9dd4-f2bb6f90bf43";
            string clientId = "614c03d3-a424-4a35-89e4-2b13e4e12ec8";
            string clientSecret = "****************************************";

            ClientSecretCredential credential = new(tenantId, clientId, clientSecret);
            GraphServiceClient graphClient = new(credential);

            var message = await graphClient.Users[userAddress]
                .Messages[emailId]
                .GetAsync(requestConfig =>
                {
                    requestConfig.QueryParameters.Select = new[] { "body" };
                });

            return message?.Body?.Content;
        }

        public async Task<EmailDetails?> GetEmailDetailsAsync(string userAddress, string emailId)
        {
            string tenantId = "eda89739-547c-4048-9dd4-f2bb6f90bf43";
            string clientId = "614c03d3-a424-4a35-89e4-2b13e4e12ec8";
            string clientSecret = "****************************************";

            ClientSecretCredential credential = new(tenantId, clientId, clientSecret);
            GraphServiceClient graphClient = new(credential);

            var message = await graphClient.Users[userAddress]
                .Messages[emailId]
                .GetAsync(requestConfig =>
                {
                    requestConfig.QueryParameters.Select = new[] { "body", "from", "toRecipients", "subject", "ccRecipients", "receivedDateTime" };
                });

            if (message == null)
                return null;

            return new EmailDetails
            {
                Id = emailId,
                From = message.From?.EmailAddress?.Address,
                ToRecipients = message.ToRecipients?.Select(r => r.EmailAddress?.Address).ToList(),
                Subject = message.Subject,
                Body = message.Body?.Content,
                CcRecipients = message.CcRecipients?.Select(r => r.EmailAddress?.Address).ToList(),
                ReceivedDateTime = message.ReceivedDateTime
            };
        }

        public async Task<EmailTemplate> GetEmailTemplateAsync(string templateName)
        {
            var emailTemplate =  await dbContext.EmailTemplates.SingleOrDefaultAsync(t => t.Name == templateName);
            if(emailTemplate == null)
            {
                throw new KeyNotFoundException($"Can't find email template {templateName}");
            }
            return emailTemplate;
        }

        public async Task SendEmailFromTemplate(string toAddress, string templateName, dynamic model)
        {
            var emailTemplate = await GetEmailTemplateAsync(templateName);

            string? subject = null;
            string? body = null;

            if (fluidParser.TryParse(emailTemplate.Subject, out var template, out var error))
            {
                var context = new TemplateContext(model);
                subject = template.Render(context);
            }
            else
            {
                Console.WriteLine($"Error: {error}");
                throw new Exception($"Error parsing email template error");
            }

            if (fluidParser.TryParse(emailTemplate.Body, out template, out error))
            {
                var context = new TemplateContext(model);
                body = template.Render(context);
            }
            else
            {
                Console.WriteLine($"Error: {error}");
                throw new Exception($"Error parsing email template error");
            }

            await SendAsync("<EMAIL>", toAddress, subject, body);
        }
        

        public class EmailSummary
        {
            public string? Id { get; set; }
            public string? Subject { get; set; }
            public Recipient? From { get; set; }
            public List<Recipient>? ToRecipients { get; set; }
            public List<Recipient>? CcRecipients { get; set; }
            public DateTimeOffset? ReceivedDateTime { get; set; }
            public bool? HasAttachments { get; set; }
            public string? BodyPreview { get; set; }
        }

        public class EmailDetails
        {
            public string? Id { get; set; }
            public string? From { get; set; }
            public List<string?>? ToRecipients { get; set; }
            public string? Subject { get; set; }
            public string? Body { get; set; }
            public List<string?>? CcRecipients { get; set; }
            public DateTimeOffset? ReceivedDateTime { get; set; }
        }
    }
}
