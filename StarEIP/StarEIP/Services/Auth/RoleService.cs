using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;

namespace StarEIP.Services.Auth;

public interface IRoleService
{
    Task<IEnumerable<ApplicationRole>> GetAllRolesAsync();
    Task<ApplicationRole?> GetRoleByIdAsync(int roleId);
    Task<IdentityResult> CreateRoleAsync(ApplicationRole role);
    Task<IdentityResult> CreateRoleAsync(string roleName, string? description = null);
    Task<IdentityResult> UpdateRoleAsync(ApplicationRole role);
    Task<IdentityResult> DeleteRoleAsync(ApplicationRole role);
}

public class RoleService : IRoleService
{
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly ILogger<RoleService> _logger;

    public RoleService(RoleManager<ApplicationRole> roleManager, ILogger<RoleService> logger)
    {
        _roleManager = roleManager;
        _logger = logger;
    }

    public async Task<IEnumerable<ApplicationRole>> GetAllRolesAsync()
    {
        return await _roleManager.Roles.ToListAsync();
    }

    public async Task<ApplicationRole?> GetRoleByIdAsync(int roleId)
    {
        return await _roleManager.FindByIdAsync(roleId.ToString());
    }

    public Task<IdentityResult> CreateRoleAsync(ApplicationRole role)
    {
        throw new NotImplementedException();
    }

    public async Task<IdentityResult> CreateRoleAsync(string roleName, string? description = null)
    {
        try
        {
            var role = new ApplicationRole { Name = roleName, Description = description };
            return await _roleManager.CreateAsync(role);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error creating role: {e.Message}");
            throw;
        }
    }

    public async Task<IdentityResult> UpdateRoleAsync(ApplicationRole role)
    {
        try
        {
            var existingRole = await _roleManager.FindByIdAsync(role.Id.ToString());
            if (existingRole == null)
                return IdentityResult.Failed(new IdentityError { Description = $"Role not found: {role.Id}" });

            existingRole.Name = role.Name;
            existingRole.Description = role.Description;
            return await _roleManager.UpdateAsync(existingRole);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error updating role: {e.Message}");
            throw;
        }
    }

    public async Task<IdentityResult> DeleteRoleAsync(ApplicationRole role)
    {
        try
        {
            var existingRole = await _roleManager.FindByIdAsync(role.Id.ToString());
            if (existingRole == null)
                return IdentityResult.Failed(new IdentityError { Description = $"Role not found: {role.Id}" });

            return await _roleManager.DeleteAsync(existingRole);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error deleting role: {e.Message}");
            throw;
        }
    }
}