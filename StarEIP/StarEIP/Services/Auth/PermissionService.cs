using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using StarEIP.Models.Auth;

namespace StarEIP.Services.Auth;

public interface IPermissionService
{
    Task<IdentityResult> AddPermissionToUserAsync(int userId, string permission);
    Task<IdentityResult> RevokePermissionFromUserAsync(int userId, string permission);
}

public class PermissionService : IPermissionService
{
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<PermissionService> _logger;

    public PermissionService(RoleManager<ApplicationRole> roleManager,
        UserManager<ApplicationUser> userManager,
        ILogger<PermissionService> logger)
    {
        _roleManager = roleManager;
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<IdentityResult> AddPermissionToUserAsync(int userId, string permission)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = $"User with ID {userId} not found." });
            var claim = new Claim("Permission", permission.ToString());
            return await _userManager.AddClaimAsync(user, claim);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while adding permission to user");
            throw;
        }
    }

    public async Task<IdentityResult> RevokePermissionFromUserAsync(int userId, string permission)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = $"User with ID {userId} not found." });
            var claim = new Claim("Permission", permission.ToString());
            return await _userManager.RemoveClaimAsync(user, claim);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error revoking permission from user");
            throw;
        }
    }

}