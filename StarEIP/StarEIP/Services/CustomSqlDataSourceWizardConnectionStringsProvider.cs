using DevExpress.DataAccess.ConnectionParameters;
using DevExpress.DataAccess.Web;
using System.Data.SqlClient;

public class CustomSqlDataSourceWizardConnectionStringsProvider(IConfiguration configuration, ILogger<CustomSqlDataSourceWizardConnectionStringsProvider> logger) : IDataSourceWizardConnectionStringsProvider
{

    public Dictionary<string, string> GetConnectionDescriptions()
    {
        logger.LogInformation("GetConnectionDescriptions called");
        return new Dictionary<string, string>
        {
            { "DefaultConnection", "Application Database" }
        };
    }

    public DataConnectionParametersBase? GetDataConnectionParameters(string name)
    {
        logger.LogInformation("GetDataConnectionParameters called with name: {name}", name);

        if (name == "DefaultConnection")
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            logger.LogInformation("Connection string: {connectionString}", connectionString);

            if (string.IsNullOrEmpty(connectionString))
            {
                logger.LogError("Connection string is null or empty");
                return null;
            }

            var builder = new SqlConnectionStringBuilder(connectionString);
            var parameters = new MsSqlConnectionParameters
            {
                ServerName = builder.DataSource,
                DatabaseName = builder.InitialCatalog,
                UserName = builder.IntegratedSecurity ? null : builder.UserID,
                Password = builder.IntegratedSecurity ? null : builder.Password,
                AuthorizationType = builder.IntegratedSecurity ? MsSqlAuthorizationType.Windows : MsSqlAuthorizationType.SqlServer,
                TrustServerCertificate = builder.TrustServerCertificate ? DevExpress.Utils.DefaultBoolean.True : DevExpress.Utils.DefaultBoolean.Default,
            };
            return parameters;
        }

        logger.LogWarning("No connection parameters found for name: {name}", name);
        return null;
    }
}
