﻿using DevExpress.DataAccess.ConnectionParameters;
using DevExpress.DataAccess.Sql;
using DevExpress.XtraReports.UI;
using Microsoft.Data.SqlClient;
using StarEIP.Models;

namespace StarEIP.Services
{
    public class CustomReportStorageWebExtension : DevExpress.XtraReports.Web.Extensions.ReportStorageWebExtension
    {
        private readonly StarEipDbContext dbContext;
        private readonly IConfiguration configuration;

        public CustomReportStorageWebExtension(StarEipDbContext dbContext, IConfiguration configuration)
        {
            this.dbContext = dbContext;
            this.configuration = configuration;
        }

        public override bool CanSetData(string url) => true;

        public override bool IsValidUrl(string url) => true;

        public override byte[] GetData(string url)
        {
            var reportData = dbContext.Reports.FirstOrDefault(x => x.Name == url);
            if (reportData != null)
            {
                using var stream = new MemoryStream(reportData.LayoutData);
                var report = new XtraReport();
                report.LoadLayoutFromXml(stream);
                if (report.DataSource is DevExpress.DataAccess.Sql.SqlDataSource connection)
                {
                    var connectionString = configuration.GetConnectionString(connection.ConnectionName);
                    var builder = new SqlConnectionStringBuilder(connectionString);
                    var parameters = new MsSqlConnectionParameters(
                        serverName: builder.DataSource,
                        databaseName: builder.InitialCatalog,
                        userName: builder.IntegratedSecurity ? null : builder.UserID,
                        password: builder.IntegratedSecurity ? null : builder.Password,
                        authorizationType: builder.IntegratedSecurity ? MsSqlAuthorizationType.Windows : MsSqlAuthorizationType.SqlServer,
                        encrypt: DevExpress.Utils.DefaultBoolean.Default,
                        trustServerCertificate: builder.TrustServerCertificate ? DevExpress.Utils.DefaultBoolean.True : DevExpress.Utils.DefaultBoolean.Default
                        );
                    connection.ConnectionParameters = parameters;
                }
                using var updatedStream = new MemoryStream();
                report.SaveLayoutToXml(updatedStream);
                return updatedStream.ToArray();
            }

            throw new DevExpress.XtraReports.Web.ClientControls.FaultException(string.Format("Could not find report '{0}'.", url));
        }


        public override Task AfterGetDataAsync(string url, XtraReport report)
        {
            return base.AfterGetDataAsync(url, report);
        }

        public override Dictionary<string, string> GetUrls()
        {
            return dbContext.Reports
                .ToList()
                .Select(x => x.Name)
                .ToDictionary<string, string>(x => x);
        }

        public override void SetData(XtraReport report, string url)
        {
            using var stream = new MemoryStream();
            report.SaveLayoutToXml(stream);
            var reportData = dbContext.Reports.FirstOrDefault(x => x.Name == url);
            if (reportData == null)
            {
                dbContext.Reports.Add(new ReportItem { Name = url, LayoutData = stream.ToArray(), DisplayName = report.DisplayName });
            }
            else
            {
                reportData.LayoutData = stream.ToArray();
                reportData.DisplayName = report.DisplayName;
            }
            dbContext.SaveChanges();
        }

        public override string SetNewData(XtraReport report, string defaultUrl)
        {
            SetData(report, defaultUrl);
            return defaultUrl;
        }
    }
}