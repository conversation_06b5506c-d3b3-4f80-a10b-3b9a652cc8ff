@page "/blazor/"
@rendermode InteractiveServer
@using Flurl
@using Flurl.Http
@layout Shared.NoLayout

<PageTitle>Counter</PageTitle>

<DxPdfViewer CssClass="w-100 h-100 pdf-viewer" DocumentContent="file" />

@code {
    
    byte[]? file = null;

    protected async override Task OnInitializedAsync()
    {
        file = await "https://api.phone.com/v4/accounts"
                   .AppendPathSegments("3112115", "fax", "ef36af2e1fce294c38c270324f51e62ccc863817", "download")
                   .SetQueryParams(new { offset = 0, limit = 25, mode = "full" })
                   .WithHeader("accept", "application/json")
                   .WithOAuthBearerToken("9BTlTVg60yvF3PksQ38JXMsjdNYhRK0tgxhpV9dpdLOvTnlI")
                   .GetBytesAsync();

        await base.OnInitializedAsync();
    }
}
