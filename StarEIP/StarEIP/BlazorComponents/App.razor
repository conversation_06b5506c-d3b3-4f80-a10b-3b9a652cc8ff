@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Mvc.ViewFeatures

<!DOCTYPE html>
<html lang="en" style="height: 100%;width: 100%">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link href="_content/DevExpress.Blazor.Themes/blazing-berry.bs5.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/open-iconic/1.1.0/font/css/open-iconic-bootstrap.min.css">
    <link rel="stylesheet" href="_content/DevExpress.Blazor.Reporting.Viewer/css/dx-blazor-reporting-components.css">
    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="StarEIP.styles.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    @DxResourceManager.RegisterScripts()
    <HeadOutlet />
</head>

<body style="height: 100%;width: 100%">
    <Routes />
    <script src="_framework/blazor.web.js"></script>


    <script>
        window.registerBlazorComponent = function(blazorComponentRef) {
            window.addEventListener("message", function(event) {
                //const faxId = event.data;
                //if (faxId && typeof faxId === 'string') {
                    // Call the instance method using the registered DotNetObjectReference
                  //  blazorComponentRef.invokeMethodAsync('ReceiveFaxId', faxId)
                    //    .catch(error => console.error(error));
               // } else {
                 //   console.error('Invalid faxId:', faxId);
               // }
               
               const { id, type } = event.data;
               if (id && typeof id === 'string' && typeof type === 'string') {
                        blazorComponentRef.invokeMethodAsync('ReceiveDocumentId', id, type)
                        .catch(error => console.error(error));
               } else {
                    console.error('Invalid document data:', event.data);
               }
            });
        };
    </script>
</body>
</html>

@code {

}