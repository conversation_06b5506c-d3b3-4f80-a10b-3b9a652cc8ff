﻿using AspNetSerilog.Extensions;
using JsonMasking;
using Newtonsoft.Json;
using Serilog;
using System.Xml.Linq;

namespace StarEIP.Middleware
{
    public static class LogHelper
    {
        //inpired from https://github.com/ThiagoBarradas/aspnet-serilog/blob/master/AspNetSerilog/Extractors/HttpContextExtractor.cs

        private static string[] blacklist = { "password", "" };

        public static void EnrichFromRequest(IDiagnosticContext diagnosticContext, HttpContext httpContext)
        {
            var request = httpContext.Request;

            // Set all the common properties available for every request
            diagnosticContext.Set("Host", request.Host);
            diagnosticContext.Set("Protocol", request.Protocol);
            diagnosticContext.Set("Scheme", request.Scheme);
            diagnosticContext.Set("Method", request.Method);
            diagnosticContext.Set("Headers", request.Headers);
            diagnosticContext.Set("ContentType", httpContext.Response.ContentType);
            diagnosticContext.Set("IPAddress", httpContext.Connection.RemoteIpAddress);
            diagnosticContext.Set("IP-Forwarded", GetIp(httpContext));
            diagnosticContext.Set("FullUrl", GetFullUrl(httpContext));


            if (httpContext.Items.ContainsKey("RequestPayload"))
            {
                string? bodyString = httpContext.Items["RequestPayload"]?.ToString();
                var contentType = (httpContext.Request.Headers.ContainsKey("Content-Type")) ? string.Join(";", httpContext.Request.Headers["Content-Type"].ToArray()) : string.Empty;

                var isJson = (contentType.Contains("json") == true);
                var isXml = (contentType.Contains("xml") == true);
                object requestBody;

                if (isJson)
                {
                    requestBody = GetContentAsObjectByContentTypeJson(bodyString, true, blacklist);
                }
                else if (isXml)
                {
                    requestBody = GetContentAsObjectByContentTypeXml(bodyString, true, blacklist);
                }
                else
                {
                    requestBody = new Dictionary<string, string> { { "raw_body", bodyString } };
                }


                diagnosticContext.Set("RequestBody", requestBody);
            }

            // Only set it if available. You're not sending sensitive data in a querystring right?!
            if (request.QueryString.HasValue)
            {
                diagnosticContext.Set("QueryString", request.QueryString.Value);
            }

            // Retrieve the IEndpointFeature selected for the request
            var endpoint = httpContext.GetEndpoint();
            if (endpoint is object) // endpoint != null
            {
                diagnosticContext.Set("EndpointName", endpoint.DisplayName);
            }

            diagnosticContext.Set("IsAuthenticated", httpContext.User.Identity?.IsAuthenticated);
            if ((httpContext.User.Identity?.IsAuthenticated).GetValueOrDefault())
            {
                var userInfo = new
                {
                    JwtGuid = httpContext.User.GetJwtGuid(),
                    userId = httpContext.User.GetUserId()
                };
                diagnosticContext.Set("UserInfo", userInfo, true);
                diagnosticContext.Set("TokenGuid", httpContext.User.GetJwtGuid(), true);
            }
        }

        internal static object GetContentAsObjectByContentTypeJson(string content, bool maskJson, string[] backlist)
        {
            try
            {
                if (maskJson == true && backlist?.Any() == true)
                {
                    content = content.MaskFields(backlist, "******");
                }

                return content.DeserializeAsObject();
            }
            catch (Exception) { }

            return content;
        }

        internal static object GetContentAsObjectByContentTypeXml(string content, bool maskXml, string[] blacklist)
        {
            string xmlConverted = null;
            using (var reader = new StringReader(content))
            {
                XDocument xml = XDocument.Parse(reader.ReadToEnd());
                xmlConverted = JsonConvert.SerializeXNode(xml);
            }

            return GetContentAsObjectByContentTypeJson(xmlConverted, maskXml, blacklist);
        }

        public static string GetIp(this HttpContext context)
        {
            var deafultIp = "??";

            if (context?.Request?.Headers == null)
            {
                return deafultIp;
            }

            if (context.Request.Headers.Any(r => r.Key == "X-Forwarded-For") == true)
            {
                return context.Request.Headers["X-Forwarded-For"].First();
            }

            return context.Connection?.RemoteIpAddress?.ToString() ?? deafultIp;
        }

        public static object GetFullUrl(this HttpContext context)
        {
            var absoluteUri = string.Concat(
                       context?.Request?.Scheme,
                       "://",
                       context?.Request?.Host.ToUriComponent(),
                       context?.Request?.PathBase.ToUriComponent(),
                       context?.Request?.Path.ToUriComponent());

            return absoluteUri;
        }
    }
}
