﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class ImportChildInfoAuthorization : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Import_ChildInfoAuthorization",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BillingProvider = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EipProviderId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiChildId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildMiddleName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Dob = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ChildAddress = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildCity = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildState = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildZip = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Sex = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CountyOfResidence = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    DateAgingOut = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ServiceCoordinatorFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ServiceCoordinatorLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ServiceCoordinatorCompany = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ServiceCoordinatorPhone = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ServiceCoordinatorEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodPhone = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Program = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EnrollmentType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EnrollmentStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AuthorizationNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LocationType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SuspendedStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SuspendedEndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Length = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Frequency = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FrequencyUnit = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    VisitsPerDay = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    IfspId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    IfspSignedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IfspType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    IfspStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    IfspStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IfspEndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExitDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TherapistFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    TherapistLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    TherapistNpi = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferringProviderFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferringProviderLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferringProviderNpi = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    LastSessionDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TotalSessionsAuthorized = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalSessionsUsed = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    NumberOfMakeupUnitsAuthorized = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalMakeupUnitsUsed = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MakeupUnitsRemaining = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalCoVisitUnits = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CoVisitUnitsUsed = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CoVisitUnitsRemaining = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RemainingUnitsSessions = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastImportedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Import_ChildInfoAuthorization", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Import_ChildInfoAuthorization_EiChildId_AuthorizationNumber",
                table: "Import_ChildInfoAuthorization",
                columns: new[] { "EiChildId", "AuthorizationNumber" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Import_ChildInfoAuthorization");
        }
    }
}
