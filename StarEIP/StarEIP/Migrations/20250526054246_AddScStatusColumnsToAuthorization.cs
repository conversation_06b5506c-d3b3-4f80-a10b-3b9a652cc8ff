using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    public partial class AddScStatusColumnsToAuthorization : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ScStatus",
                table: "Authorization",
                type: "nvarchar(255)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "StatusLastUpdated",
                table: "Authorization",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "FollowUpDate",
                table: "Authorization",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ScStatusLastUpdated",
                table: "Authorization",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ScStatus",
                table: "Authorization");

            migrationBuilder.DropColumn(
                name: "StatusLastUpdated",
                table: "Authorization");

            migrationBuilder.DropColumn(
                name: "FollowUpDate",
                table: "Authorization");

            migrationBuilder.DropColumn(
                name: "ScStatusLastUpdated",
                table: "Authorization");
        }
    }
}
