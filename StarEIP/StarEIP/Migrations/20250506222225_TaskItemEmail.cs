﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class TaskItemEmail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaskItem_AspNetUsers_AssignedToUserId",
                table: "TaskItem");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskItem_TaskStatus_TaskStatusId",
                table: "TaskItem");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskItem_TaskTemplate_TaskTemplateId",
                table: "TaskItem");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskItemLink_TaskItem_TaskItemId",
                table: "TaskItemLink");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskTemplate",
                table: "TaskTemplate");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskStatus",
                table: "TaskStatus");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskItemLink",
                table: "TaskItemLink");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskItem",
                table: "TaskItem");

            migrationBuilder.RenameTable(
                name: "TaskTemplate",
                newName: "TaskTemplates");

            migrationBuilder.RenameTable(
                name: "TaskStatus",
                newName: "TaskStatuses");

            migrationBuilder.RenameTable(
                name: "TaskItemLink",
                newName: "TaskItemLinks");

            migrationBuilder.RenameTable(
                name: "TaskItem",
                newName: "TaskItems");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItemLink_TaskItemId",
                table: "TaskItemLinks",
                newName: "IX_TaskItemLinks_TaskItemId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItemLink_LinkTable_LinkId",
                table: "TaskItemLinks",
                newName: "IX_TaskItemLinks_LinkTable_LinkId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItem_TaskTemplateId",
                table: "TaskItems",
                newName: "IX_TaskItems_TaskTemplateId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItem_TaskStatusId",
                table: "TaskItems",
                newName: "IX_TaskItems_TaskStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItem_AssignedToUserId",
                table: "TaskItems",
                newName: "IX_TaskItems_AssignedToUserId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskTemplates",
                table: "TaskTemplates",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskStatuses",
                table: "TaskStatuses",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskItemLinks",
                table: "TaskItemLinks",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskItems",
                table: "TaskItems",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "EmailMessages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GraphMessageId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    InternetMessageId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Subject = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SenderEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ReceivedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    WebLink = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailMessages", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaskItemEmails",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TaskItemId = table.Column<int>(type: "int", nullable: false),
                    EmailMessageId = table.Column<int>(type: "int", nullable: false),
                    LinkType = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskItemEmails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskItemEmails_EmailMessages_EmailMessageId",
                        column: x => x.EmailMessageId,
                        principalTable: "EmailMessages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaskItemEmails_TaskItems_TaskItemId",
                        column: x => x.TaskItemId,
                        principalTable: "TaskItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TaskItemEmails_EmailMessageId",
                table: "TaskItemEmails",
                column: "EmailMessageId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItemEmails_TaskItemId",
                table: "TaskItemEmails",
                column: "TaskItemId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItemLinks_TaskItems_TaskItemId",
                table: "TaskItemLinks",
                column: "TaskItemId",
                principalTable: "TaskItems",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItems_AspNetUsers_AssignedToUserId",
                table: "TaskItems",
                column: "AssignedToUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItems_TaskStatuses_TaskStatusId",
                table: "TaskItems",
                column: "TaskStatusId",
                principalTable: "TaskStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItems_TaskTemplates_TaskTemplateId",
                table: "TaskItems",
                column: "TaskTemplateId",
                principalTable: "TaskTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaskItemLinks_TaskItems_TaskItemId",
                table: "TaskItemLinks");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskItems_AspNetUsers_AssignedToUserId",
                table: "TaskItems");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskItems_TaskStatuses_TaskStatusId",
                table: "TaskItems");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskItems_TaskTemplates_TaskTemplateId",
                table: "TaskItems");

            migrationBuilder.DropTable(
                name: "TaskItemEmails");

            migrationBuilder.DropTable(
                name: "EmailMessages");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskTemplates",
                table: "TaskTemplates");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskStatuses",
                table: "TaskStatuses");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskItems",
                table: "TaskItems");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskItemLinks",
                table: "TaskItemLinks");

            migrationBuilder.RenameTable(
                name: "TaskTemplates",
                newName: "TaskTemplate");

            migrationBuilder.RenameTable(
                name: "TaskStatuses",
                newName: "TaskStatus");

            migrationBuilder.RenameTable(
                name: "TaskItems",
                newName: "TaskItem");

            migrationBuilder.RenameTable(
                name: "TaskItemLinks",
                newName: "TaskItemLink");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItems_TaskTemplateId",
                table: "TaskItem",
                newName: "IX_TaskItem_TaskTemplateId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItems_TaskStatusId",
                table: "TaskItem",
                newName: "IX_TaskItem_TaskStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItems_AssignedToUserId",
                table: "TaskItem",
                newName: "IX_TaskItem_AssignedToUserId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItemLinks_TaskItemId",
                table: "TaskItemLink",
                newName: "IX_TaskItemLink_TaskItemId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskItemLinks_LinkTable_LinkId",
                table: "TaskItemLink",
                newName: "IX_TaskItemLink_LinkTable_LinkId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskTemplate",
                table: "TaskTemplate",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskStatus",
                table: "TaskStatus",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskItem",
                table: "TaskItem",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskItemLink",
                table: "TaskItemLink",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItem_AspNetUsers_AssignedToUserId",
                table: "TaskItem",
                column: "AssignedToUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItem_TaskStatus_TaskStatusId",
                table: "TaskItem",
                column: "TaskStatusId",
                principalTable: "TaskStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItem_TaskTemplate_TaskTemplateId",
                table: "TaskItem",
                column: "TaskTemplateId",
                principalTable: "TaskTemplate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItemLink_TaskItem_TaskItemId",
                table: "TaskItemLink",
                column: "TaskItemId",
                principalTable: "TaskItem",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
