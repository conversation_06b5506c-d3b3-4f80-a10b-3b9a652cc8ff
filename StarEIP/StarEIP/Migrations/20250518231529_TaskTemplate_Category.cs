﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class TaskTemplate_Category : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Category",
                table: "TaskTemplates",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SubCategory",
                table: "TaskTemplates",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Temp<PERSON><PERSON><PERSON>",
                table: "TaskTemplates",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "ChildId",
                table: "TaskItems",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskItems_ChildId",
                table: "TaskItems",
                column: "ChildId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskItems_Child_ChildId",
                table: "TaskItems",
                column: "ChildId",
                principalTable: "Child",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaskItems_Child_ChildId",
                table: "TaskItems");

            migrationBuilder.DropIndex(
                name: "IX_TaskItems_ChildId",
                table: "TaskItems");

            migrationBuilder.DropColumn(
                name: "Category",
                table: "TaskTemplates");

            migrationBuilder.DropColumn(
                name: "SubCategory",
                table: "TaskTemplates");

            migrationBuilder.DropColumn(
                name: "TemplateKey",
                table: "TaskTemplates");

            migrationBuilder.DropColumn(
                name: "ChildId",
                table: "TaskItems");
        }
    }
}
