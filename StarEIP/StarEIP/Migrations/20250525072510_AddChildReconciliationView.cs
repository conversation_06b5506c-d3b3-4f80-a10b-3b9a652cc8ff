﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddChildReconciliationView : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE VIEW vw_ChildReconciliation AS
                SELECT
                  COALESCE(h.EiChildId, p.ProgramId, CAST(s.Id AS NVARCHAR(50))) AS ChildId,

                  -- Basic info (favor HUB if available)
                  COALESCE(h.ChildFirstName + ' ' + h.ChildLastName, p.ChildName, s.FirstName + ' ' + s.LastName) AS ChildName,
                  COALESCE(h.Dob, p.DateOfBirth, s.DateOfBirth) AS DOB,

                  -- Presence indicators
                  CASE WHEN h.EiChildId IS NOT NULL THEN 1 ELSE 0 END AS ExistsInHub,
                  CASE WHEN p.ProgramId IS NOT NULL THEN 1 ELSE 0 END AS ExistsInPs,
                  CASE WHEN s.Id IS NOT NULL THEN 1 ELSE 0 END AS ExistsInStar,

                  -- Matching checks (using DOB and ProgramId for matching)
                  CASE
                    WHEN h.EiChildId IS NOT NULL AND p.ProgramId IS NOT NULL AND h.Dob = p.DateOfBirth THEN 1 ELSE 0
                  END AS HubPsMatch,

                  CASE
                    WHEN h.EiChildId IS NOT NULL AND s.Id IS NOT NULL AND h.Dob = s.DateOfBirth THEN 1 ELSE 0
                  END AS HubStarMatch,

                  CASE
                    WHEN p.ProgramId IS NOT NULL AND s.Id IS NOT NULL AND p.DateOfBirth = s.DateOfBirth THEN 1 ELSE 0
                  END AS PsStarMatch,

                  -- Additional useful fields
                  h.EiChildId AS HubChildId,
                  p.ProgramId AS PsChildId,
                  s.Id AS StarChildId,
                  s.ProgramId AS StarProgramId

                FROM Import_ChildInfoAuthorization h
                FULL OUTER JOIN Import_PsAllChildren p ON h.EiChildId = p.ProgramId
                FULL OUTER JOIN Child s ON s.ProgramId = h.EiChildId;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP VIEW IF EXISTS vw_ChildReconciliation;");
        }
    }
}
