﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class TaskItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReconciliationEntries");

            migrationBuilder.CreateTable(
                name: "Import_ReconciliationEntry",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ChildName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SessionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ClaimCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SubType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    BillAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    RemittStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    RemittDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    FundingSource = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Discrepancy = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    AdjustReasonCodes = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AdjustReasonDescription = table.Column<string>(type: "varchar(8000)", nullable: false),
                    Reconciled = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastImportedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Import_ReconciliationEntry", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaskStatus",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskStatus", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaskTemplate",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaskItem",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    TaskTemplateId = table.Column<int>(type: "int", nullable: false),
                    TaskStatusId = table.Column<int>(type: "int", nullable: false),
                    AssignedToUserId = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DueAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskItem", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskItem_AspNetUsers_AssignedToUserId",
                        column: x => x.AssignedToUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_TaskItem_TaskStatus_TaskStatusId",
                        column: x => x.TaskStatusId,
                        principalTable: "TaskStatus",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaskItem_TaskTemplate_TaskTemplateId",
                        column: x => x.TaskTemplateId,
                        principalTable: "TaskTemplate",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TaskItemLink",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TaskItemId = table.Column<int>(type: "int", nullable: false),
                    LinkTable = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    LinkId = table.Column<int>(type: "int", nullable: false),
                    LinkType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskItemLink", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskItemLink_TaskItem_TaskItemId",
                        column: x => x.TaskItemId,
                        principalTable: "TaskItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Import_ReconciliationEntry_EiNumber_Provider_SessionDate_Type_SubType",
                table: "Import_ReconciliationEntry",
                columns: new[] { "EiNumber", "Provider", "SessionDate", "Type", "SubType" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskItem_AssignedToUserId",
                table: "TaskItem",
                column: "AssignedToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItem_TaskStatusId",
                table: "TaskItem",
                column: "TaskStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItem_TaskTemplateId",
                table: "TaskItem",
                column: "TaskTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItemLink_LinkTable_LinkId",
                table: "TaskItemLink",
                columns: new[] { "LinkTable", "LinkId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskItemLink_TaskItemId",
                table: "TaskItemLink",
                column: "TaskItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Import_ReconciliationEntry");

            migrationBuilder.DropTable(
                name: "TaskItemLink");

            migrationBuilder.DropTable(
                name: "TaskItem");

            migrationBuilder.DropTable(
                name: "TaskStatus");

            migrationBuilder.DropTable(
                name: "TaskTemplate");

            migrationBuilder.CreateTable(
                name: "ReconciliationEntries",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AdjustReasonCodes = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AdjustReasonDescription = table.Column<string>(type: "varchar(8000)", nullable: false),
                    BillAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ChildName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ClaimCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Discrepancy = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EiNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FundingSource = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    LastImportedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Reconciled = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    RemittDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    RemittStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SessionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SubType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReconciliationEntries", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReconciliationEntries_EiNumber_Provider_SessionDate_Type_SubType",
                table: "ReconciliationEntries",
                columns: new[] { "EiNumber", "Provider", "SessionDate", "Type", "SubType" },
                unique: true);
        }
    }
}
