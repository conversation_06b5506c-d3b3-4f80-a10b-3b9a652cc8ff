﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddBaseEntityToChild : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "Child",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedOn",
                table: "Child",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "Child",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedOn",
                table: "Child",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "Child",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedOn",
                table: "Child",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "CreatedOn",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "DeletedOn",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "UpdatedOn",
                table: "Child");
        }
    }
}
