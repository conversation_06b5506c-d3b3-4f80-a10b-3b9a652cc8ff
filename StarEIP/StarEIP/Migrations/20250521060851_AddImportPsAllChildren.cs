﻿﻿﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddImportPsAllChildren : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Import_PsAllChildren",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ChildName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    DateOfBirth = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Address = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    City = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    State = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Zip = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ProgramId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Region = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    OngoingServiceCoordinator = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CurrentOcStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CurrentOcEndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PrimaryLanguage = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Stage = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ProgramReferralDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Gender = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    DateOfIntake = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateOfFirstContact = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ReferralSource = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastImportedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Import_PsAllChildren", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Import_PsAllChildren_ProgramId",
                table: "Import_PsAllChildren",
                column: "ProgramId");

            // Add PS_ALL_CHILDREN import type
            migrationBuilder.InsertData(
                table: "ImportTypes",
                columns: new[] { "Name", "Description", "ImportKey" },
                values: new object[] { "PS All Children", "Import PS All Children data", "PS_ALL_CHILDREN" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove PS_ALL_CHILDREN import type
            migrationBuilder.DeleteData(
                table: "ImportTypes",
                keyColumn: "ImportKey",
                keyValue: "PS_ALL_CHILDREN");

            migrationBuilder.DropTable(
                name: "Import_PsAllChildren");
        }
    }
}
