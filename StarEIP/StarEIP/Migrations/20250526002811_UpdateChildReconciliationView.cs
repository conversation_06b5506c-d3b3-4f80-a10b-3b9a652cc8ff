﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class UpdateChildReconciliationView : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop the existing view
            migrationBuilder.Sql("DROP VIEW IF EXISTS vw_ChildReconciliation;");

            // Create the updated view with new columns
            migrationBuilder.Sql(@"
                ALTER VIEW [dbo].[vw_ChildReconciliation] AS
                SELECT
                    CONCAT(h.id, '~', p.id, '~', s.id) AS RowKey,
                    CONVERT(VARCHAR(250), COALESCE(h.EiChildId, p.ProgramId, s.ProgramId)) AS ChildId,

                    -- Basic info (favor HUB if available)
                    COALESCE(h.ChildFirstName + ' ' + h.<PERSON>ame, p.ChildName, s.FirstName + ' ' + s.LastName) AS ChildName,
                    h.<PERSON>,
                    h.<PERSON>,
                    h.<PERSON>,
                    h.<PERSON>,
                    COAL<PERSON><PERSON>(h<PERSON>, p.<PERSON>, s.<PERSON>f<PERSON>irth) AS DOB,
                    h.<PERSON>tName,
                    h.GuardianLastName,
                    CONCAT(h.Address, h.City, h.State, h.Zip) AS FullAddress,

                    -- Presence indicators
					CONVERT(BIT, CASE WHEN h.EiChildId IS NOT NULL THEN 1 ELSE 0 END) ExistsInHub,
					CONVERT(BIT, CASE WHEN p.ProgramId IS NOT NULL THEN 1 ELSE 0 END) ExistsInPs,
					CONVERT(BIT, CASE WHEN s.Id IS NOT NULL THEN 1 ELSE 0 END) ExistsInStar,

                    -- Matching checks
                    CONVERT(BIT, CASE WHEN h.EiChildId IS NOT NULL AND p.ProgramId IS NOT NULL AND h.Dob = p.DateOfBirth THEN 1 ELSE 0 END) AS HubPsMatch,
                    CONVERT(BIT, CASE WHEN h.EiChildId IS NOT NULL AND s.Id IS NOT NULL AND h.Dob = s.DateOfBirth THEN 1 ELSE 0 END) AS HubStarMatch,
                    CONVERT(BIT, CASE WHEN p.ProgramId IS NOT NULL AND s.Id IS NOT NULL AND p.DateOfBirth = s.DateOfBirth THEN 1 ELSE 0 END) AS PsStarMatch,

                    -- Additional useful fields
                    h.EiChildId AS HubChildId,
                    p.ProgramId AS PsChildId,
                    s.Id AS StarChildId,
                    s.ProgramId AS StarProgramId,

                    -- Authorization check
                    CONVERT(BIT, CASE 
                        WHEN EXISTS (
                            SELECT 1 
                            FROM Import_ChildInfoAuthorization a 
                            WHERE a.EiChildId = COALESCE(h.EiChildId, p.ProgramId, s.ProgramId)
                              AND a.StartDate <= GETDATE()
                              AND a.EndDate >= GETDATE()
                        ) THEN 1 ELSE 0 
                    END) AS HasActiveAuth

                FROM Import_ChildDetailReport h
                FULL OUTER JOIN Import_PsAllChildren p ON h.EiChildId = p.ProgramId
                FULL OUTER JOIN Child s ON s.ProgramId = h.EiChildId;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop the updated view
            migrationBuilder.Sql("DROP VIEW IF EXISTS vw_ChildReconciliation;");

            // Recreate the original view
            migrationBuilder.Sql(@"
                CREATE VIEW vw_ChildReconciliation AS
                SELECT
                  COALESCE(h.EiChildId, p.ProgramId, CAST(s.Id AS NVARCHAR(50))) AS ChildId,

                  -- Basic info (favor HUB if available)
                  COALESCE(h.ChildFirstName + ' ' + h.ChildLastName, p.ChildName, s.FirstName + ' ' + s.LastName) AS ChildName,
                  COALESCE(h.Dob, p.DateOfBirth, s.DateOfBirth) AS DOB,

                  -- Presence indicators
                  CASE WHEN h.EiChildId IS NOT NULL THEN 1 ELSE 0 END AS ExistsInHub,
                  CASE WHEN p.ProgramId IS NOT NULL THEN 1 ELSE 0 END AS ExistsInPs,
                  CASE WHEN s.Id IS NOT NULL THEN 1 ELSE 0 END AS ExistsInStar,

                  -- Matching checks (using DOB and ProgramId for matching)
                  CASE
                    WHEN h.EiChildId IS NOT NULL AND p.ProgramId IS NOT NULL AND h.Dob = p.DateOfBirth THEN 1 ELSE 0
                  END AS HubPsMatch,

                  CASE
                    WHEN h.EiChildId IS NOT NULL AND s.Id IS NOT NULL AND h.Dob = s.DateOfBirth THEN 1 ELSE 0
                  END AS HubStarMatch,

                  CASE
                    WHEN p.ProgramId IS NOT NULL AND s.Id IS NOT NULL AND p.DateOfBirth = s.DateOfBirth THEN 1 ELSE 0
                  END AS PsStarMatch,

                  -- Additional useful fields
                  h.EiChildId AS HubChildId,
                  p.ProgramId AS PsChildId,
                  s.Id AS StarChildId,
                  s.ProgramId AS StarProgramId

                FROM Import_ChildInfoAuthorization h
                FULL OUTER JOIN Import_PsAllChildren p ON h.EiChildId = p.ProgramId
                FULL OUTER JOIN Child s ON s.ProgramId = h.EiChildId;
            ");
        }
    }
}
