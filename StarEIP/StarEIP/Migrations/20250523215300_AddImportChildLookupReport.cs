﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddImportChildLookupReport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Import_ChildLookupReport",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EiChildId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    BirthDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Phone = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Address = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    City = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    State = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Zip = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    County = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CountyOfResidence = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ServiceCoordinator = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastImportedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Import_ChildLookupReport", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Import_ChildLookupReport_EiChildId",
                table: "Import_ChildLookupReport",
                column: "EiChildId");

            // Add HUB_CHILD_LOOKUP_REPORT import type
            migrationBuilder.InsertData(
                table: "ImportTypes",
                columns: new[] { "Name", "Description", "ImportKey" },
                values: new object[] { "HUB Child Lookup Report", "Import HUB Child Lookup Report data", "HUB_CHILD_LOOKUP_REPORT" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove HUB_CHILD_LOOKUP_REPORT import type
            migrationBuilder.DeleteData(
                table: "ImportTypes",
                keyColumn: "ImportKey",
                keyValue: "HUB_CHILD_LOOKUP_REPORT");

            migrationBuilder.DropTable(
                name: "Import_ChildLookupReport");
        }
    }
}
