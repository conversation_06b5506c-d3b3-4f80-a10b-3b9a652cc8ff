using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddScStatusTypeTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create ScStatusType table
            migrationBuilder.CreateTable(
                name: "ScStatusType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScStatusType", x => x.Id);
                });

            // Add initial status types
            migrationBuilder.InsertData(
                table: "ScStatusType",
                columns: new[] { "Name", "SortOrder" },
                values: new object[,]
                {
                    { "Pending", 1 },
                    { "In Progress", 2 },
                    { "Completed", 3 },
                    { "Needs Follow-up", 4 },
                    { "On Hold", 5 }
                });

            // Add temporary column for the migration
            migrationBuilder.AddColumn<int>(
                name: "ScStatusId",
                table: "Authorization",
                type: "int",
                nullable: true);

            // Create a default mapping for existing values
            migrationBuilder.Sql(@"
                UPDATE a
                SET a.ScStatusId = s.Id
                FROM Authorization a
                JOIN ScStatusType s ON a.ScStatus = s.Name
                WHERE a.ScStatus IS NOT NULL
            ");

            // Drop the old column
            migrationBuilder.DropColumn(
                name: "ScStatus",
                table: "Authorization");

            // Rename the new column to the original name
            migrationBuilder.RenameColumn(
                name: "ScStatusId",
                table: "Authorization",
                newName: "ScStatus");

            // Add foreign key constraint
            migrationBuilder.AddForeignKey(
                name: "FK_Authorization_ScStatusType_ScStatus",
                table: "Authorization",
                column: "ScStatus",
                principalTable: "ScStatusType",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove foreign key constraint
            migrationBuilder.DropForeignKey(
                name: "FK_Authorization_ScStatusType_ScStatus",
                table: "Authorization");

            // Add temporary string column
            migrationBuilder.AddColumn<string>(
                name: "ScStatusString",
                table: "Authorization",
                type: "nvarchar(max)",
                nullable: true);

            // Copy data back to string column
            migrationBuilder.Sql(@"
                UPDATE a
                SET a.ScStatusString = s.Name
                FROM Authorization a
                JOIN ScStatusType s ON a.ScStatus = s.Id
                WHERE a.ScStatus IS NOT NULL
            ");

            // Drop the int column
            migrationBuilder.DropColumn(
                name: "ScStatus",
                table: "Authorization");

            // Rename string column back to original name
            migrationBuilder.RenameColumn(
                name: "ScStatusString",
                table: "Authorization",
                newName: "ScStatus");

            // Drop the ScStatusType table
            migrationBuilder.DropTable(
                name: "ScStatusType");
        }
    }
}

