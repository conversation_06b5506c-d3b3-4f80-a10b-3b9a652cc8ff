﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;


namespace StarEIP.Extensions
{
    public static class ObjectComparer
    {
        public static bool AreEqual<T>(T obj1, T obj2, params string[] propertiesToIgnore)
        {
            if (obj1 == null || obj2 == null)
                return obj1 == null && obj2 == null;

            var type = typeof(T);
            var ignored = new HashSet<string>(propertiesToIgnore);

            foreach (PropertyInfo prop in type.GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                if (ignored.Contains(prop.Name))
                    continue;

                var value1 = prop.GetValue(obj1);
                var value2 = prop.GetValue(obj2);

                if (!object.Equals(value1, value2))
                    return false;
            }

            return true;
        }
    }

}
