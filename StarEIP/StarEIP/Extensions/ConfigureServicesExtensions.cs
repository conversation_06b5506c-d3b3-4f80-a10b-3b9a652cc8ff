﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerUI;
using System.Diagnostics;
using System.Net;
using System.Reflection;
using System.Text;

namespace StarEIP.Extensions
{
    public static class ConfigureServicesExtensions
    {
        public static void AddBearerAuthentication(this IHostApplicationBuilder builder)
        {
            var secretKey = builder.Configuration["JwtSettings:SecretKey"];
            if (string.IsNullOrWhiteSpace(secretKey))
            {
                throw new ArgumentNullException("JwtSettings:SecretKey is required");
            }
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var authBuilder = builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultSignInScheme = JwtBearerDefaults.AuthenticationScheme;
            });

            authBuilder.AddJwtBearer(options =>
            {
                options.TokenValidationParameters.ValidateIssuerSigningKey = true;
                options.TokenValidationParameters.IssuerSigningKey = securityKey;
                options.TokenValidationParameters.ValidateLifetime = true;

                options.RequireHttpsMetadata = false;

                //options.Authority = builder.Configuration["JwtSettings:TokenIssuer"];
                options.TokenValidationParameters.ValidIssuer = builder.Configuration["JwtSettings:TokenIssuer"];

                options.TokenValidationParameters.ValidAudience = builder.Configuration["JwtSettings:TokenAudience"];
                options.Audience = builder.Configuration["JwtSettings:TokenAudience"];

                options.Events = new JwtBearerEvents
                {
                    OnChallenge = context =>
                    {
                        // Skip the default logic.
                        context.HandleResponse();

                        // Return a 401 status code.
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        context.Response.ContentType = "application/json";
                        return context.Response.WriteAsync("{\"error\": \"Unauthorized\"}");
                    },
                    OnMessageReceived = context =>
                    {
                        var path = context.HttpContext.Request.Path;
                        if (path.StartsWithSegments("/hub"))
                        {
                            var accessToken = context.Request.Query["access_token"];

                            // If the request is for our hub...
                            if (!string.IsNullOrEmpty(accessToken))
                            {
                                // Read the token out of the query string
                                context.Token = accessToken;
                            }
                        }
                        return System.Threading.Tasks.Task.CompletedTask;
                    }
                };
            });
        }

        public static void AddStarEIPSwagger(this IServiceCollection services)
        {
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "StarEIP Web API", Version = "v1" });

                // Set the comments path for the Swagger JSON and UI.
                var xmlFile = $"{Assembly.GetEntryAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (System.IO.File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }

                c.CustomSchemaIds(x => x.FullName);

                c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme()
                {
                    Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
                    In = Microsoft.OpenApi.Models.ParameterLocation.Header,
                    Name = "Authorization",
                    Description = "JWT Authorization header using the Bearer Scheme. \"Authorization: Bearer {token}\"",
                    Scheme = "Bearer"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                  {
                    {
                      new OpenApiSecurityScheme
                      {
                        Reference = new OpenApiReference
                          {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                          },
                          Scheme = "oauth2",
                          Name = "Bearer",
                          In = ParameterLocation.Header,
                        },
                        new List<string>()
                      }
                    });
                c.OrderActionsBy(api => api.RelativePath);
            });
        }

        public static IApplicationBuilder UseStarEIPSwagger(this IApplicationBuilder app)
        {
            app.UseSwagger();

            return app.UseSwaggerUI(c =>
            {
                c.DocumentTitle = "StarEIP Web API V1 Explorer";
                c.EnableFilter();
                c.DocExpansion(DocExpansion.None);
                c.ShowExtensions();
                c.EnablePersistAuthorization();
                c.DisplayRequestDuration();
                c.EnableTryItOutByDefault();
                c.DisplayOperationId();
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Brands Web API");
            });
        }

        public static void AddStarEIPStaticFiles(this IApplicationBuilder app)
        {
            app.UseStaticFiles(new StaticFileOptions
            {
                HttpsCompression = Microsoft.AspNetCore.Http.Features.HttpsCompressionMode.Compress,
                OnPrepareResponse = ctx =>
                {
                    if (Path.GetExtension(ctx.File.Name) != ".html")
                    {
                        double durationInSeconds = TimeSpan.FromDays(7).TotalSeconds;
                        ctx.Context.Response.Headers[Microsoft.Net.Http.Headers.HeaderNames.CacheControl] = "public,max-age=" + durationInSeconds;
                    }
                }
            });
        }

        public static IApplicationBuilder UseStarEIPExceptionHandler(this IApplicationBuilder builder)
        {
            builder.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";
                    var contextFeature = context.Features.Get<Microsoft.AspNetCore.Diagnostics.IExceptionHandlerFeature>();
                    if (contextFeature != null)
                    {
                        if (contextFeature is Microsoft.AspNetCore.Diagnostics.ExceptionHandlerFeature error)
                        {
                            var message = Debugger.IsAttached ? error.Error.ToString() : "Internal server error";
                            //logger.LogError(error.Error, "Something went wrong. Path: {Path}", error.Path);
                            await context.Response.WriteAsJsonAsync(new
                            {
                                StatusCode = context.Response.StatusCode,
                                Message = message
                            }.ToString());
                        }
                        else
                        {
                            //logger.LogError(contextFeature.Error, "Something went wrong. (for some reason the error dosnt have a path)");
                            await context.Response.WriteAsJsonAsync(new
                            {
                                StatusCode = context.Response.StatusCode,
                                Message = "Internal server error"
                            }.ToString());
                        }
                    }
                });
            });

            return builder;
        }
    }
}
