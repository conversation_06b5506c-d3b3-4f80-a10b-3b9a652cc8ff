using StarEIP.Models.Common;
using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models
{
    [Table("ChildAttachments")]
    public class ChildAttachment : BaseEntity
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string? FileType { get; set; }
        public string? Description { get; set; }

        public int ChildId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public virtual Child? Child { get; set; }
    }
}