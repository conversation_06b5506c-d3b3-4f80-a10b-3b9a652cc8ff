using CsvHelper.Configuration.Attributes;

namespace StarEIP.Models.Import
{
    public class ChildLookupReportCsv
    {
        [Name("EI Child ID")]
        public string EiChildId { get; set; } = string.Empty;

        [Name("Last Name")]
        public string LastName { get; set; } = string.Empty;

        [Name("First Name")]
        public string FirstName { get; set; } = string.Empty;

        [Name("Birth Date")]
        public DateTime? BirthDate { get; set; }

        [Name("Phone")]
        public string Phone { get; set; } = string.Empty;

        [Name("Address")]
        public string Address { get; set; } = string.Empty;

        [Name("City")]
        public string City { get; set; } = string.Empty;

        [Name("State")]
        public string State { get; set; } = string.Empty;

        [Name("Zip")]
        public string Zip { get; set; } = string.Empty;

        [Name("County")]
        public string County { get; set; } = string.Empty;

        [Name("County of Residence")]
        public string CountyOfResidence { get; set; } = string.Empty;

        [Name("EIOD Name")]
        public string EiodName { get; set; } = string.Empty;

        [Name("Service Coordinator")]
        public string ServiceCoordinator { get; set; } = string.Empty;

        [Name("Child Status")]
        public string ChildStatus { get; set; } = string.Empty;
    }
}
