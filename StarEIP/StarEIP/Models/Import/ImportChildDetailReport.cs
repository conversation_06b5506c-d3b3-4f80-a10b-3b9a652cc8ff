using System;

namespace StarEIP.Models.Import
{
    public class ImportChildDetailReport
    {
        public int Id { get; set; }
        
        public string EiChildId { get; set; }
        public string ChildLastName { get; set; }
        public string ChildFirstName { get; set; }
        public DateTime? Dob { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Gender { get; set; }
        public string Ethnicity { get; set; }
        public string Race { get; set; }
        public string ChildAge { get; set; }
        public string PrimaryLanguage { get; set; }
        public string CountyOfResidence { get; set; }
        public string AddressCounty { get; set; }
        public DateTime? ReferralDate { get; set; }
        public DateTime? AtRiskReferralDate { get; set; }
        public string ReferralAtRiskStatus { get; set; }
        public string ReferralType { get; set; }
        public string ReferralMethod { get; set; }
        public string ReferralReason { get; set; }
        public string ReferralSourceType { get; set; }
        public string EligibilityStatus { get; set; }
        public string GuardianLastName { get; set; }
        public string GuardianFirstName { get; set; }
        public string GuardianPhone { get; set; }
        public string CoordinatorLastName { get; set; }
        public string CoordinatorFirstName { get; set; }
        public string CoordinatorPhone { get; set; }
        public string CoordinatorCompany { get; set; }
        public string EiodLastName { get; set; }
        public string EiodFirstName { get; set; }
        public DateTime? CurrentIfspStartDate { get; set; }
        public DateTime? CurrentIfspEndDate { get; set; }
        public string CurrentIfspStatus { get; set; }
        public string CurrentIfspType { get; set; }
        
        // Audit fields
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime LastImportedAt { get; set; }
    }
}
