using System.ComponentModel.DataAnnotations;

namespace StarEIP.Models.Import
{
    public class ImportChildLookupReport
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(255)]
        public string EiChildId { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string FirstName { get; set; } = string.Empty;

        public DateTime? BirthDate { get; set; }

        [MaxLength(255)]
        public string Phone { get; set; } = string.Empty;

        [MaxLength(255)]
        public string Address { get; set; } = string.Empty;

        [MaxLength(255)]
        public string City { get; set; } = string.Empty;

        [MaxLength(255)]
        public string State { get; set; } = string.Empty;

        [MaxLength(255)]
        public string Zip { get; set; } = string.Empty;

        [MaxLength(255)]
        public string County { get; set; } = string.Empty;

        [MaxLength(255)]
        public string CountyOfResidence { get; set; } = string.Empty;

        [MaxLength(255)]
        public string EiodName { get; set; } = string.Empty;

        [MaxLength(255)]
        public string ServiceCoordinator { get; set; } = string.Empty;

        [MaxLength(255)]
        public string ChildStatus { get; set; } = string.Empty;

        // Audit fields
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime LastImportedAt { get; set; }
    }
}
