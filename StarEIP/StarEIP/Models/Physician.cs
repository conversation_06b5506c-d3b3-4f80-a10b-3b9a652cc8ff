﻿using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models
{
    [Table("Physician")]
    public class Physician
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? PhoneNumber { get; set; }
        public string? EmailAddress { get; set; }
        public string? StreetAddress { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }

        public string? Notes { get; set; }
        public string? FacilityName { get; set; }
        public ReferralSourceType ReferralSourceType { get; set; }
        

        public ICollection<Child>? Children { get; set; }
    }

    public enum ReferralSourceType
    {
        EIProvider,
        Hospital,
        HealthcareProvider,
        ParentFamily,
        SocialServiceAgency
    }
}
