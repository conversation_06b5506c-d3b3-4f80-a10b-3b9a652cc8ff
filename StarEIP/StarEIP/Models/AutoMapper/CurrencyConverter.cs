﻿using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;
using System.Globalization;

namespace StarEIP.Models.AutoMapper
{
    public class CurrencyConverter : DecimalConverter
    {
        public override object ConvertFromString(string? text, IReaderRow row, MemberMapData memberMapData)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 0m;

            text = text.Trim()
                       .Replace("$", "")
                       .Replace("(", "-")
                       .Replace(")", "");

            if (decimal.TryParse(text, NumberStyles.Any, CultureInfo.InvariantCulture, out var value))
                return value;

            return base.ConvertFromString(text, row, memberMapData);
        }
    }
}
