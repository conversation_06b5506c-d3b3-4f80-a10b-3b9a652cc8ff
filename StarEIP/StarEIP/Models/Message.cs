using StarEIP.Models.Common;

namespace StarEIP.Models;

public class SmsMessage: BaseEntity
{
    public string From { get; set; }
    public string To { get; set; }
    public string Text { get; set; }
    public string Direction { get; set; } // inbound outbound 
    public DateTime CreatedAt { get; set; }
    public string ExternalMessageId { get; set; }
    public int? ChildId { get; set; }
    public Child? Child { get; set; }
}