﻿namespace StarEIP.Models.Tasks
{
    public class TaskItemEmail
    {
        public int Id { get; set; }

        public int TaskItemId { get; set; }
        public TaskItem TaskItem { get; set; } = default!;

        public int EmailMessageId { get; set; }
        public EmailMessage EmailMessage { get; set; } = default!;

        // NEW: Why this email is linked to this task
        public EmailLinkType LinkType { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public enum EmailLinkType
    {
        CreatedTask = 0,   // The email caused the task to be created
        CreatedFromTask = 1, // The email was sent/generated as a result of the task
        Linked = 2          // The email is related but not the cause or result
    }

}
