namespace StarEIP.Models.Tasks
{
    public class TaskTemplate
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string TemplateKey { get; set; }

        public string Category { get; set; }
        public string? SubCategory { get; set; }

        public ICollection<TaskItem> TaskItems { get; set; } = new List<TaskItem>();
    }
}