﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models
{
    public class Fax
    {
        [Key]
        public int Id { get; set; }
        public string FaxId { get; set; }
        public string FromNumber { get; set; }
        public string To { get; set; }
        public int Pages { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Direction { get; set; }
        public string DownloadUrl { get; set; }
        public bool PdfSaved { get; set; }
        public string FileName { get; set; } // The name property from Telebroad API, used as the file parameter

        public int? PhysicianId { get; set; }

        [ForeignKey(nameof(PhysicianId))]
        public virtual Physician Physician { get; set; }
    }
}
