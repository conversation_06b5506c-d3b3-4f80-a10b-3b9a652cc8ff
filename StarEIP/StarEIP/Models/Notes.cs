﻿using StarEIP.Models.Common;

namespace StarEIP.Models {
    public class Notes: BaseEntity {
        public string Note { get; set; }

        // Keep for backward compatibility
        public int? ChildId { get; set; }
        public Child? Child { get; set; }

        // New properties for generic entity linking
        public string? EntityType { get; set; } = "child"; // Default to 'child' for backward compatibility
        public int? EntityId { get; set; }
    }
}
