﻿using System.ComponentModel.DataAnnotations.Schema;
using StarEIP.Models.Common;

namespace StarEIP.Models
{
    [Table("Child")]
    public class Child: BaseEntity
    {
        public string? Status { get; set; } = "New";
        public DateTime? StatusLastUpdated { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        
        public string? PatientFullName { get; set; }
        public string? Gender { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? PrimaryLanguage { get; set; }
        public string? ReasonForReferral { get; set; }

        public string? ParentName { get; set; }
        public string? ParentPhoneNumber { get; set; }
        public string? ParentEmail { get; set; }
        public string? FullAddress { get; set; }

        public string? ReferringPhysicianName { get; set; }
        public string? PhysicianPhoneNumber { get; set; }
        public string? PhysicianEmailAddress { get; set; }

        public string? ReferralMethod { get; set; }
        public int? ReferringPhysicianId { get; set; }

        public Physician? ReferringPhysician { get; set; }

        public int? ProgramId { get; set; }
        public int? ProviderSoftChildId { get; set; }

        [ForeignKey("ChildId")]
        public ICollection<Authorization>? Authorizations { get; set; } = null;
        
        [ForeignKey("ChildId")]
        public ICollection<ChildAttachment>? ChildAttachments { get; set; } = null;
        
        [ForeignKey("ChildId")]
        public ICollection<Notes>? Notes { get; set; } = null;
    }
}
