using System.Text.Json;
using System.Text.Json.Serialization;

namespace StarEIP.Models.App.TelebroadApi
{
    // Type aliases for specific response types
    public class TelebroadSmsConversationsResponse : TelebroadBaseResponse<List<TelebroadSmsConversation>> { }
    public class TelebroadSmsConversationResponse : TelebroadBaseResponse<List<TelebroadSmsMessage>> { }

    // For compatibility with existing code
    public class TelebroadListResponse<T> : TelebroadBaseResponse<List<T>> { }

    public class TelebroadSmsConversation
    {
        [JsonPropertyName("id")]
        [JsonConverter(typeof(StringOrNumberConverter))]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("lid")]
        public string Lid { get; set; } = string.Empty;

        [JsonPropertyName("idx")]
        public int Idx { get; set; }

        [JsonPropertyName("new")]
        public int New { get; set; }

        [JsonPropertyName("direction")]
        public string Direction { get; set; } = string.Empty;

        [JsonPropertyName("time")]
        public string Time { get; set; } = string.Empty;

        [JsonPropertyName("last_message")]
        public string LastMessage { get; set; } = string.Empty;
    }

    public class TelebroadSmsMessage
    {
        [JsonPropertyName("id")]
        [JsonConverter(typeof(StringOrNumberConverter))]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("lid")]
        [JsonConverter(typeof(StringOrNumberConverter))]
        public string Lid { get; set; } = string.Empty;

        [JsonPropertyName("idx")]
        public int Idx { get; set; }

        [JsonPropertyName("new")]
        public int New { get; set; }

        [JsonPropertyName("direction")]
        public string Direction { get; set; } = string.Empty;

        [JsonPropertyName("sender")]
        [JsonConverter(typeof(StringOrNumberConverter))]
        public string Sender { get; set; } = string.Empty;

        [JsonPropertyName("receiver")]
        [JsonConverter(typeof(StringOrNumberConverter))]
        public string Receiver { get; set; } = string.Empty;

        [JsonPropertyName("time")]
        public long Time { get; set; }

        [JsonPropertyName("msgdata")]
        public string Msgdata { get; set; } = string.Empty;

        [JsonPropertyName("media")]
        public List<string> Media { get; set; } = [];

        [JsonPropertyName("read")]
        public int Read { get; set; }

        [JsonPropertyName("read_by")]
        public string ReadBy { get; set; } = string.Empty;

        [JsonPropertyName("sent_by")]
        [JsonConverter(typeof(StringOrNumberConverter))]
        public string SentBy { get; set; }

        [JsonPropertyName("seen")]
        public int Seen { get; set; }

        // Aliases for compatibility with existing code
        [JsonIgnore]
        public string From => Direction == "in" ? Sender : Receiver;

        [JsonIgnore]
        public string To => Direction == "in" ? Receiver : Sender;

        [JsonIgnore]
        public string Text => Msgdata;
    }
}
