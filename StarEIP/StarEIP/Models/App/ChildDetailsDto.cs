using System.Linq.Expressions;

namespace StarEIP.Models
{
    public class ChildDetailsDto
    {
        public int Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PatientName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; }
        public string? PrimaryLanguage { get; set; }
        public string? ReasonForReferral { get; set; }
        public string? ParentName { get; set; }
        public string? ParentPhoneNumber { get; set; }
        public string? ParentEmail { get; set; }
        public string? FullAddress { get; set; }
        public string? ReferringPhysicianName { get; set; }
        public string? PhysicianPhoneNumber { get; set; }
        public string? PhysicianEmailAddress { get; set; }
        public string? ReferralMethod { get; set; }
        public int? ReferringPhysicianId { get; set; }
        public int? ProgramId { get; set; }
        public int? ProviderSoftChildId { get; set; }
        public string? Status { get; set; }
        public DateTime? StatusLastUpdated { get; set; }
        public int NotesCount { get; set; }
        public int AuthorizationsCount { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
    }
}