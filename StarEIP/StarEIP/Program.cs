using Azure.Storage.Blobs;
using Destructurama;
using DevExpress.AspNetCore.Reporting;
using DevExpress.Blazor;
using DevExpress.Blazor.Reporting;
using DevExpress.XtraReports.Security;
using DevExpress.XtraReports.Services;
using DevExpress.XtraReports.Web.Extensions;
using DevExpress.XtraReports.Web.WebDocumentViewer;
using Fluid;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using Serilog.Exceptions;
using Serilog.Exceptions.Core;
using Serilog.Exceptions.Destructurers;
using Serilog.Exceptions.EntityFrameworkCore.Destructurers;
using Serilog.Exceptions.MsSqlServer.Destructurers;
using StarEIP.BlazorComponents;
using StarEIP.Configuration;
using StarEIP.Extensions;
using StarEIP.Middleware;
using StarEIP.Models.App;
using StarEIP.Models.Auth;
using StarEIP.Models.AutoMapper;
using StarEIP.Models.Import;
using StarEIP.Services;
using StarEIP.Services.Auth;
using StarEIP.Services.Core;
using StarEIP.Services.Data;
using System.Diagnostics;
using System.Reflection;

namespace StarEIP
{
    public class Program
    {
        public static Guid AppInstanceId = Guid.NewGuid();

        public static string? Version => Assembly.GetEntryAssembly()?.GetCustomAttribute<System.Reflection.AssemblyInformationalVersionAttribute>()?.InformationalVersion;

        public static void Main(string[] args)
        {
            ScriptPermissionManager.GlobalInstance = new ScriptPermissionManager(ExecutionMode.Unrestricted);
            LoggerConfiguration logConfiguration = ConfigureSerilogLogger();
            Log.Logger = logConfiguration.CreateLogger();

            var builder = WebApplication.CreateBuilder(args);

            builder.Services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                });

            builder.Services.AddDbContext<StarEipDbContext>(options =>
            {
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
            });

            builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
            {
                options.SignIn.RequireConfirmedAccount = true;
            }).AddEntityFrameworkStores<StarEipDbContext>()
            .AddDefaultTokenProviders();

            builder.Host.UseSerilog(Log.Logger, false);
            builder.Services.AddSerilog(Log.Logger);

            builder.Services.Configure<DataProtectionTokenProviderOptions>(o =>
            {
                o.Name = "Brands Paydeck Identity";
                o.TokenLifespan = TimeSpan.FromHours(72);
            });
            builder.AddBearerAuthentication();
            builder.Services.AddAuthorization(options =>
            {
                var properties = typeof(UserPermission).GetProperties();
                foreach (var permissionProperty in properties.Where(p => !new string[] { nameof(UserPermission.PermissionClaimName) }.Contains(p.Name)))
                {
                    options.AddPolicy(permissionProperty.Name, policy =>
                    {
                        policy.RequireClaim
                        (
                            UserPermission.PermissionClaimName,
                            permissionProperty.Name
                        );
                    });
                }
            });
            builder.Services.AddControllers();
            builder.Services.AddStarEIPSwagger();

            builder.Services.AddCors(options =>
            {
                options.AddPolicy("AllowLocalhost3000",
                    builder => builder.WithOrigins("http://localhost:3000", "https://app.stareip.com",
                            "http://app.stareip.com")
                        .AllowAnyHeader()
                        .AllowCredentials()
                        .AllowAnyMethod());
            });

            builder.Services.AddDataProtection().SetApplicationName("StarEIP");
            builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
            builder.Services.Configure<Configuration.TelebroadApiOptions>(
                builder.Configuration.GetSection(Configuration.TelebroadApiOptions.TelebroadApi));
            builder.Host.UseSerilog(Log.Logger, false);

            builder.Services.AddScoped<EmailService>();
            builder.Services.AddScoped<IUserService, UserService>();
            builder.Services.AddScoped<IRoleService, RoleService>();
            builder.Services.AddScoped<IPermissionService, PermissionService>();
            builder.Services.AddScoped<ChildDataService>();

            builder.Services.AddSingleton(new FluidParser(new FluidParserOptions { AllowFunctions = true }));
            builder.Services.AddRazorComponents().AddInteractiveServerComponents(x => x.DetailedErrors = true);
            builder.Services.AddCascadingAuthenticationState();

            builder.Services.AddDevExpressBlazor(configure => configure.BootstrapVersion = BootstrapVersion.v5);
            builder.Services.AddDevExpressServerSideBlazorReportViewer();
            builder.Services.AddDevExpressBlazorReporting();
            builder.Services.AddScoped<ReportStorageWebExtension, CustomReportStorageWebExtension>();
            builder.Services.AddScoped<DocumentOperationService, CustomDocumentOperationService>();
            builder.Services.AddScoped<DocumentStorageService>();
            builder.Services.AddScoped<TelebroadApiService>();

            builder.Services.ConfigureReportingServices(configurator =>
            {
                configurator.ConfigureReportDesigner(designerConfigurator =>
                {
                    designerConfigurator.RegisterDataSourceWizardConnectionStringsProvider<CustomSqlDataSourceWizardConnectionStringsProvider>();
                });
                configurator.ConfigureWebDocumentViewer(viewerConfigurator =>
                {
                    viewerConfigurator.UseCachedReportSourceBuilder();
                    viewerConfigurator.RegisterConnectionProviderFactory<CustomSqlDataConnectionProviderFactory>();
                });
                configurator.UseAsyncEngine();
            });

            builder.Services.AddAntiforgery(x => x.SuppressXFrameOptionsHeader = true);
            builder.Services.PostConfigure<ApiBehaviorOptions>(options =>
            {
                var builtInFactory = options.InvalidModelStateResponseFactory;
                options.InvalidModelStateResponseFactory = context =>
                {
                    var errors = context.ModelState.Select(s => s.Value?.Errors).SelectMany(s => s);
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    logger.LogError(errors.FirstOrDefault()?.Exception, "Error during model validating {@ModelError}",
                        errors);
                    return builtInFactory(context);
                };
            });
            builder.Services.AddSingleton(x => new BlobServiceClient(builder.Configuration.GetConnectionString("StorageAccount")));

            // Add configuration to services so it can be injected
            builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
           // builder.Services.AddHostedService<SmsBackgroundService>();
           // builder.Services.AddHostedService<FaxBackgroundService>();
            builder.Services.AddHostedService<EmailTaskSyncService>();
            builder.Services.AddScoped<ImportService>();
            builder.Services.AddScoped<ImportReconciliationEntry>();
            builder.Services.AddAutoMapper(typeof(ImportCsvMappingProfile));

            var app = builder.Build();
            app.UseMiddleware<RequestResponseLoggingMiddleware>();
            app.UseSerilogRequestLogging(opts => opts.EnrichDiagnosticContext = Middleware.LogHelper.EnrichFromRequest);

            app.UseExceptionHandling();
            app.UseStarEIPExceptionHandler();

            app.UseDevExpressBlazorReporting();
            app.UseReporting(builder =>
            {
                builder.UserDesignerOptions.DataBindingMode = DevExpress.XtraReports.UI.DataBindingMode.Expressions;
            });

            app.UseStarEIPSwagger();
            app.UseHttpsRedirection();
            app.AddStarEIPStaticFiles();
            app.UseCors("AllowLocalhost3000");
            app.UseAuthentication();
            app.UseAuthorization();

            app.UsePathBase("/blazor");
            app.UseAntiforgery();
            app.UseStaticFiles();
            app.MapRazorComponents<App>().AddInteractiveServerRenderMode();
            app.MapControllers();

            //#if !API
            //            app.MapWhen(CheckRouteNotApi, builder =>
            //            {
            //                builder.UseSpa(spa =>
            //                {
            //                    spa.Options.SourcePath = "ClientApp";
            //                    if (app.Environment.IsDevelopment() && app.Configuration.GetValue<string>("RunNpmStart") != "NO")
            //                    {
            //                        Log.Logger.Debug("In Debug Mode");
            //                        //spa.UseReactDevelopmentServer(npmScript: "dev");
            //                    }
            //                });
            //            });
            //#endif

            try
            {
                Log.Logger.Debug("Starting Application");
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Logger.Fatal(ex, "Application Failed to Start");
                Log.CloseAndFlush();
            }
        }

        private static LoggerConfiguration ConfigureSerilogLogger()
        {
            var destructuringOptions = new DestructuringOptionsBuilder()
                .WithDefaultDestructurers()
                .WithDestructurers(new List<IExceptionDestructurer>
                {
                    new DbUpdateExceptionDestructurer(),
                    new SqlExceptionDestructurer()
                });

            var logConfiguration = new Serilog.LoggerConfiguration()
                .Destructure.JsonNetTypes()
                .Enrich.WithMachineName()
                .Enrich.WithThreadId()
                .Enrich.WithProcessId()
                .Enrich.WithProperty("AppInstanceId", AppInstanceId)
                .Enrich.WithProperty("AppID", "StarEIP")
                .Enrich.WithProperty("Version", Version)
                .Enrich.FromLogContext()
                .Enrich.WithExceptionDetails(destructuringOptions)
                .MinimumLevel.Verbose()
                .WriteTo.Seq("http://www.1800dev.com:5341", Serilog.Events.LogEventLevel.Information,
                    apiKey: "CPEGMDKwtR5oa5J0zWwp")
                .WriteTo.Console(Serilog.Events.LogEventLevel.Verbose);

            logConfiguration.MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning);

            Serilog.Debugging.SelfLog.Enable(l =>
            {
                Debug.WriteLine(l);
                Console.WriteLine(l);
            });
            return logConfiguration;
        }

        static private bool CheckRouteNotApi(HttpContext context)
        {
            return !context.Request.Path.Value.StartsWith("/api") && !context.Request.Path.Value.StartsWith("/blazor");
        }
    }
}
